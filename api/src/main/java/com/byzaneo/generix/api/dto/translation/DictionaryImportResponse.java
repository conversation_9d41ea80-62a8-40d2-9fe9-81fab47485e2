/*
 * Copyright (c) 2025.
 */

package com.byzaneo.generix.api.dto.translation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Response DTO for dictionary import operations
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DictionaryImportResponse {
    
    /**
     * Total number of translations successfully imported
     */
    private Integer totalImported;

    /**
     * List of errors that occurred during import
     */
    private List<ImportError> errors;

    /**
     * Success message for successful imports
     */
    private String successMessage;
}
