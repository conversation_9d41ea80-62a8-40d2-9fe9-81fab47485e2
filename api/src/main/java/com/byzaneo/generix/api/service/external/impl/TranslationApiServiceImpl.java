package com.byzaneo.generix.api.service.external.impl;

import com.byzaneo.generix.api.service.external.TranslationApiService;
import com.byzaneo.generix.api.service.external.delegators.TranslationServiceDelegator;
import lombok.RequiredArgsConstructor;

import org.slf4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.io.InputStream;
import java.util.UUID;

import static org.slf4j.LoggerFactory.getLogger;

@Service(TranslationApiService.SERVICE_NAME)
@RequiredArgsConstructor
public class TranslationApiServiceImpl implements TranslationApiService {
    private static final Logger log = getLogger(TranslationApiServiceImpl.class);
    private final TranslationServiceDelegator translationServiceDelegator;

    @Override
    @Transactional
    public Response downloadDictionary(HttpServletRequest request, UUID requestId, String dictType, String lang) {
        return translationServiceDelegator.downloadDictionary(request ,requestId,dictType,lang );
    }

    @Override
    @Transactional
    public Response importDictionary(HttpServletRequest request, UUID requestId, String dictType, String lang, InputStream csvContent) {
        Response delegatorResponse = translationServiceDelegator.importDictionary(request, requestId, dictType, lang, csvContent);

        // Extract the entity for further processing
        Object entity = delegatorResponse.getEntity();

        // If we have a DictionaryImportResponse, check for import errors
        if (entity instanceof com.byzaneo.generix.api.dto.translation.DictionaryImportResponse) {
            com.byzaneo.generix.api.dto.translation.DictionaryImportResponse importResponse =
                (com.byzaneo.generix.api.dto.translation.DictionaryImportResponse) entity;

            // Count only actual errors (not success messages which have lineNumber = 0)
            long actualErrorCount = importResponse.getErrors() != null ?
                importResponse.getErrors().stream()
                    .filter(error -> error.getLineNumber() != 0)
                    .count() : 0;

            // If there are actual errors, return a 400 Bad Request status
            if (actualErrorCount > 0) {
                log.warn("Import validation errors found: {}", actualErrorCount);
                return com.byzaneo.generix.api.util.RestServiceHelper.getResponse(
                    Response.Status.BAD_REQUEST,
                    importResponse
                );
            }
        }

        // If the original response is successful and no validation errors were found
        if (delegatorResponse.getStatus() >= 200 && delegatorResponse.getStatus() < 300) {
            return com.byzaneo.generix.api.util.RestServiceHelper.getResponse(
                Response.Status.CREATED,  // Explicitly use 201 for successful imports
                entity
            );
        }

        return delegatorResponse;
    }
}
