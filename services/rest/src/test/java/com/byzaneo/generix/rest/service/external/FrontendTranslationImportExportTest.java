/*
 * Copyright (c) 2025.
 */

package com.byzaneo.generix.rest.service.external;

import com.byzaneo.angular.bean.I18NModule;
import com.byzaneo.angular.bean.I18NTranslation;
import com.byzaneo.angular.dao.I18nTranslationDAO;
import com.byzaneo.generix.api.dto.translation.DictionaryImportResponse;
import com.byzaneo.generix.api.dto.translation.ImportError;
import com.byzaneo.generix.api.service.external.delegators.TranslationServiceDelegator;
import com.byzaneo.generix.api.service.internal.impl.translation.TranslationService;
import com.byzaneo.generix.api.util.PermissionHelper;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.service.translation.I18NTranslationDto;
import com.byzaneo.security.bean.User;
import com.byzaneo.security.service.AccountService;
import com.byzaneo.query.Query;
import com.byzaneo.security.api.Right;
import com.byzaneo.security.bean.TechnicalUser;
import com.byzaneo.security.service.SecurityService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Test suite for the frontend translation import/export functionality.
 * Tests verify compliance with requirements from AIO-14744 and related JIRAs.
 */
public class FrontendTranslationImportExportTest {

    @Mock
    private SecurityService securityService;

    @Mock
    private PermissionHelper permissionHelper;

    @Mock
    private I18NService i18NService;

    @Mock
    private I18nTranslationDAO i18nTranslationDAO;

    @Mock
    private TranslationService translationService;

    @Mock
    private AccountService accountService;

    @InjectMocks
    private TranslationServiceDelegatorImpl delegator;

    private HttpServletRequest request;
    private UUID requestId;
    private TechnicalUser technicalUser;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Create a Spring mock request that implements the javax HttpServletRequest
        MockHttpServletRequest mockRequest = new MockHttpServletRequest();
        mockRequest.addHeader("Authorization", "Bearer test-token");
        request = mockRequest;
        requestId = UUID.randomUUID();

        technicalUser = new TechnicalUser();
        technicalUser.setId("1L");
        technicalUser.setDisabled(false);

        // Set up Spring Security context with TechnicalUser authentication
        // This is required for RestServiceHelper.getTechnicalUserIdFromRequest to work
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(technicalUser, null, Collections.emptyList());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        when(securityService.getTechnicalUserById(anyString())).thenReturn(technicalUser);
        when(permissionHelper.isGrantedTechnicalUser(any(), any(Right.class), eq(technicalUser))).thenReturn(true);

        // Mock AccountService for user tracking
        User mockUser = new User();
        mockUser.setLogin("testuser");
        when(accountService.getUserByLoginOrOpenID(anyString())).thenReturn(mockUser);
    }

    @AfterEach
    void tearDown() {
        // Clear the security context to avoid side effects between tests
        SecurityContextHolder.clearContext();
    }

    // EXPORT TESTS

    @Test
    @DisplayName("Export all languages when no language filter is provided")
    void testExportAllLanguages() {
        // Setup test data
        var translations = List.of(
            createTranslationDto(1L, "common", "hello", "bonjour", "hello", Locale.FRENCH),
            createTranslationDto(1L, "common", "hello", "bonjour", "hello", Locale.ENGLISH),
            createTranslationDto(1L, "common", "hello", "hallo", "hello", Locale.GERMAN),
            createTranslationDto(1L, "common", "hello", "hola", "hello", new Locale("es"))
        );
        var locales = List.of(Locale.FRENCH, Locale.ENGLISH, Locale.GERMAN, new Locale("es"));
        var modules = List.of(createModule(1L, "common"));

        when(i18NService.getTranslations()).thenReturn(translations);
        when(i18NService.getLocales()).thenReturn(locales);
        when(i18NService.findAllI18NModules()).thenReturn(modules);

        // Execute
        var response = delegator.downloadDictionary(request, requestId, "frontend", null);

        // Verify
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        assertNotNull(response.getEntity());
        var csvContent = new String((byte[]) response.getEntity(), StandardCharsets.UTF_8);

        // CSV should have module, key and all language columns (sorted by locale toString)
        // Note: Locale.toString() returns language code + potentially country code
        // so the order is determined by that, which for our test data is: de, en, es, fr
        assertTrue(csvContent.contains("module;key;de;en;es;fr") ||
                 csvContent.contains("module;key;de;en;fr;es"),
                 "CSV header should contain all languages in sorted order");
        // Check that the row has all translations (order depends on header)
        assertTrue(csvContent.contains("common;hello") &&
                 csvContent.contains("hallo") &&
                 csvContent.contains("hello") &&
                 csvContent.contains("hola") &&
                 csvContent.contains("bonjour"),
                 "CSV row should contain all translations");
    }

    @Test
    @DisplayName("Export only requested language when language filter is provided")
    void testExportSpecificLanguage() {
        // Setup test data
        var translations = List.of(
            createTranslationDto(1L, "common", "hello", "bonjour", "hello", Locale.FRENCH),
            createTranslationDto(1L, "common", "hello", "bonjour", "hello", Locale.ENGLISH),
            createTranslationDto(1L, "common", "hello", "hallo", "hello", Locale.GERMAN),
            createTranslationDto(1L, "common", "hello", "hola", "hello", new Locale("es"))
        );
        var locales = List.of(Locale.FRENCH, Locale.ENGLISH, Locale.GERMAN, new Locale("es"));
        var modules = List.of(createModule(1L, "common"));

        when(i18NService.getTranslations()).thenReturn(translations);
        when(i18NService.getLocales()).thenReturn(locales);
        when(i18NService.findAllI18NModules()).thenReturn(modules);

        // Execute
        var response = delegator.downloadDictionary(request, requestId, "frontend", "de");

        // Verify
        assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
        assertNotNull(response.getEntity());
        var csvContent = new String((byte[]) response.getEntity(), StandardCharsets.UTF_8);

        // CSV should have module, key and only the German column
        assertTrue(csvContent.contains("module;key;de"), "Header should contain only German language code");
        assertTrue(csvContent.contains("common;hello;hallo"), "CSV should contain German translations");
        // Check that other language codes don't appear in the header row
        assertFalse(csvContent.contains("fr"), "French should not be included");
        assertFalse(csvContent.contains("es"), "Spanish should not be included");
    }

    // IMPORT TESTS

    @Test
    @DisplayName("Import all non-protected languages when no language filter is provided")
    void testImportAllLanguages() {
        // Setup test data - a CSV with multiple languages
        var csvContent = "module;key;fr;en;de;es;nl\n" +
                         "common;hello;bonjour;hello;hallo;hola;hallo\n" +
                         "common;goodbye;au revoir;goodbye;auf wiedersehen;adiós;tot ziens\n";

        var inputStream = new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8));
        var module = createModule(1L, "common");

        when(i18NService.findI18NModuleByName("common")).thenReturn(Optional.of(module));
        when(i18NService.findI18NModuleById(1L)).thenReturn(module);
        when(i18nTranslationDAO.search(any(Query.class))).thenReturn(Collections.emptyList());
        when(i18nTranslationDAO.store(any(I18NTranslation.class))).thenReturn(new I18NTranslation());

        // Execute
        var response = delegator.importDictionary(request, requestId, "frontend", null, inputStream);

        // Verify
        assertEquals(Response.Status.CREATED.getStatusCode(), response.getStatus(), "Response status should be CREATED");
        var importResponse = (DictionaryImportResponse) response.getEntity();
        assertNotNull(importResponse, "Import response should not be null");

        // Should import de, es, nl (3 languages × 2 keys = 6 translations)
        assertEquals(6, importResponse.getTotalImported(), "Should import 6 translations (3 languages × 2 keys)");
        assertEquals(0, importResponse.getErrors().size(), "Should have no errors");

        // Verify that fr and en were not imported
        var translationCaptor = ArgumentCaptor.forClass(I18NTranslation.class);
        verify(i18nTranslationDAO, times(6)).store(translationCaptor.capture());

        var capturedLocales = translationCaptor.getAllValues().stream()
                .map(translation -> translation.getLocale().getLanguage())
                .distinct()
                .collect(Collectors.toSet());

        // We expect exactly 3 languages: de, es, nl
        assertEquals(3, capturedLocales.size(), "Should have imported exactly 3 distinct languages");
        assertTrue(capturedLocales.contains("de"), "German translations should be imported");
        assertTrue(capturedLocales.contains("es"), "Spanish translations should be imported");
        assertTrue(capturedLocales.contains("nl"), "Dutch translations should be imported");
        assertFalse(capturedLocales.contains("fr"), "French translations should not be imported (protected)");
        assertFalse(capturedLocales.contains("en"), "English translations should not be imported (protected)");
    }

    @Test
    @DisplayName("Import only requested language when language filter is provided")
    void testImportSpecificLanguage() {
        // Setup test data - a CSV with multiple languages
        var csvContent = "module;key;fr;en;de;es;nl\n" +
                         "common;hello;bonjour;hello;hallo;hola;hallo\n" +
                         "common;goodbye;au revoir;goodbye;auf wiedersehen;adiós;tot ziens\n";

        var inputStream = new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8));
        var module = createModule(1L, "common");

        when(i18NService.findI18NModuleByName("common")).thenReturn(Optional.of(module));
        when(i18NService.findI18NModuleById(1L)).thenReturn(module);
        when(i18nTranslationDAO.search(any(Query.class))).thenReturn(Collections.emptyList());
        when(i18nTranslationDAO.store(any(I18NTranslation.class))).thenReturn(new I18NTranslation());

        // Execute
        var response = delegator.importDictionary(request, requestId, "frontend", "de", inputStream);

        // Verify
        assertEquals(Response.Status.CREATED.getStatusCode(), response.getStatus());
        var importResponse = (DictionaryImportResponse) response.getEntity();
        assertNotNull(importResponse);

        // Should import only German (1 language × 2 keys = 2 translations)
        assertEquals(2, importResponse.getTotalImported());
        assertEquals(0, importResponse.getErrors().size());

        // Verify only German was imported
        var translationCaptor = ArgumentCaptor.forClass(I18NTranslation.class);
        verify(i18nTranslationDAO, times(2)).store(translationCaptor.capture());

        var capturedTranslations = translationCaptor.getAllValues();
        assertEquals(2, capturedTranslations.size(), "Should have captured exactly 2 translations");

        // Check that all captured translations are German
        for (I18NTranslation translation : capturedTranslations) {
            assertEquals("de", translation.getLocale().getLanguage(),
                    "All captured translations should be German");
        }
    }

    @Test
    @DisplayName("Do not modify protected languages (fr, en) during import")
    void testDoNotModifyProtectedLanguages() {
        // Setup test data - a CSV with changes to fr and en
        var csvContent = "module;key;fr;en\n" +
                         "common;hello;MODIFIED_FR;MODIFIED_EN\n" +
                         "common;goodbye;MODIFIED_FR_2;MODIFIED_EN_2\n";

        var inputStream = new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8));
        var module = createModule(1L, "common");

        when(i18NService.findI18NModuleByName("common")).thenReturn(Optional.of(module));

        // Execute
        var response = delegator.importDictionary(request, requestId, "frontend", null, inputStream);

        // Verify
        assertEquals(Response.Status.CREATED.getStatusCode(), response.getStatus());
        var importResponse = (DictionaryImportResponse) response.getEntity();
        assertNotNull(importResponse);

        // Should not import any translations since fr and en are protected
        assertEquals(0, importResponse.getTotalImported());
        // There should be no errors reported - protected languages are simply skipped
        assertEquals(0, importResponse.getErrors().size());

        // Verify no translations were stored
        verify(i18nTranslationDAO, never()).store(any(I18NTranslation.class));
    }

    // ERROR HANDLING TESTS

    @Test
    @DisplayName("Return error for invalid locale in import")
    void testImportInvalidLocale() {
        // Setup test data - a CSV with an invalid locale
        var csvContent = "module;key;fr;en;xx\n" +
                         "common;hello;bonjour;hello;invalid_locale_value\n";

        var inputStream = new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8));

        // Execute and verify
        var exception = assertThrows(IllegalArgumentException.class, () -> {
            delegator.importDictionary(request, requestId, "frontend", "xx", inputStream);
        });

        String expectedMessage = "Invalid language code: xx";
        assertTrue(exception.getMessage().contains(expectedMessage),
                "Expected message containing '" + expectedMessage + "' but got '" + exception.getMessage() + "'");
    }

    @Test
    @DisplayName("Return error for non-existing module in import")
    void testImportNonExistingModule() {
        // Setup test data - a CSV with a non-existing module
        var csvContent = "module;key;de\n" +
                         "non_existing;hello;hallo\n";

        var inputStream = new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8));

        when(i18NService.findI18NModuleByName("non_existing")).thenReturn(Optional.empty());

        // Execute
        var response = delegator.importDictionary(request, requestId, "frontend", "de", inputStream);

        // Verify
        assertEquals(Response.Status.CREATED.getStatusCode(), response.getStatus());
        var importResponse = (DictionaryImportResponse) response.getEntity();
        assertNotNull(importResponse);

        // Should not import any translations
        assertEquals(0, importResponse.getTotalImported());
        assertEquals(1, importResponse.getErrors().size());

        ImportError error = importResponse.getErrors().get(0);
        assertEquals(2, error.getLineNumber());
        assertTrue(error.getErrorMessage().contains("Unknown module: non_existing"));
    }

    @Test
    @DisplayName("Handle empty import file correctly")
    void testImportEmptyFile() {
        // Setup test data - an empty CSV
        var csvContent = "";
        var inputStream = new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8));

        // Execute
        var response = delegator.importDictionary(request, requestId, "frontend", null, inputStream);

        // Verify
        assertEquals(Response.Status.CREATED.getStatusCode(), response.getStatus());
        var importResponse = (DictionaryImportResponse) response.getEntity();
        assertNotNull(importResponse);

        assertEquals(0, importResponse.getTotalImported());
        assertEquals(1, importResponse.getErrors().size());

        ImportError error = importResponse.getErrors().get(0);
        assertEquals(0, error.getLineNumber());
        assertTrue(error.getErrorMessage().contains("File must contain at least a header and one data row"));
    }

    @Test
    @DisplayName("Handle malformed CSV header correctly")
    void testImportMalformedHeader() {
        // Setup test data - CSV with invalid header
        var csvContent = "wrong;header;format\n" +
                         "common;hello;hallo\n";

        var inputStream = new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8));

        // Execute
        var response = delegator.importDictionary(request, requestId, "frontend", null, inputStream);

        // Verify
        assertEquals(Response.Status.CREATED.getStatusCode(), response.getStatus());
        var importResponse = (DictionaryImportResponse) response.getEntity();
        assertNotNull(importResponse);

        assertEquals(0, importResponse.getTotalImported());
        assertEquals(1, importResponse.getErrors().size());

        ImportError error = importResponse.getErrors().get(0);
        assertEquals(1, error.getLineNumber());
        assertTrue(error.getErrorMessage().contains("Invalid header format") ||
                 error.getErrorMessage().contains("Header must include 'module' and 'key' columns"));
    }

    @Test
    @DisplayName("Return error for comma-separated language codes")
    void testImportMultipleSpecificLanguages() {
        // Setup test data - a CSV with multiple languages
        var csvContent = "module;key;fr;en;de;es;nl\n" +
                         "common;hello;bonjour;hello;hallo;hola;hallo\n" +
                         "common;goodbye;au revoir;goodbye;auf wiedersehen;adiós;tot ziens\n";

        var inputStream = new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8));

        // Execute and verify - API should reject comma-separated language codes
        var exception = assertThrows(IllegalArgumentException.class, () -> {
            delegator.importDictionary(request, requestId, "frontend", "de,es", inputStream);
        });

        String expectedMessage = "Invalid language code: de,es";
        assertTrue(exception.getMessage().contains(expectedMessage),
                "Expected message containing '" + expectedMessage + "' but got '" + exception.getMessage() + "'");
    }

    @Test
    @DisplayName("Enhanced error reporting for invalid locales and non-existing keys")
    void testEnhancedErrorReporting() {
        // Setup test data - CSV with invalid locale and non-existing key
        var csvContent = "module;key;fr;en;de;zz\n" +
                         "common;hello;bonjour;hello;hallo;invalid\n" +
                         "common;nonexisting;au revoir;goodbye;auf wiedersehen;invalid2\n";

        var inputStream = new ByteArrayInputStream(csvContent.getBytes(StandardCharsets.UTF_8));
        var module = createModule(1L, "common");

        when(i18NService.findI18NModuleByName("common")).thenReturn(Optional.of(module));

        // Mock that "hello" exists for fr and en, but "nonexisting" doesn't
        when(i18nTranslationDAO.search(any(Query.class))).thenAnswer(invocation -> {
            Query query = invocation.getArgument(0);
            // This is a simplified mock - in reality we'd need to check the actual query
            // For this test, we'll assume "hello" exists and "nonexisting" doesn't
            return List.of(new I18NTranslation()); // Return non-empty for existing keys
        });

        // Execute
        var response = delegator.importDictionary(request, requestId, "frontend", null, inputStream);

        // Verify
        assertEquals(Response.Status.CREATED.getStatusCode(), response.getStatus());
        var importResponse = (DictionaryImportResponse) response.getEntity();
        assertNotNull(importResponse);

        // Should have errors for invalid locale
        assertTrue(importResponse.getErrors().size() > 0, "Should have errors");

        // Check for enhanced error message format
        boolean hasInvalidLocaleError = importResponse.getErrors().stream()
            .anyMatch(error -> error.getErrorMessage().contains("Erreur [colonne") &&
                              error.getErrorMessage().contains("La locale zz n'est pas une locale reconnue"));

        assertTrue(hasInvalidLocaleError, "Should have enhanced error message for invalid locale");

        // Check that success message is in separate field if there were successful imports
        if (importResponse.getTotalImported() > 0) {
            assertNotNull(importResponse.getSuccessMessage(), "Should have success message");
            assertTrue(importResponse.getSuccessMessage().contains("Import des traductions réussi"),
                      "Success message should be in French format");
        }
    }

    // Helper methods

    private I18NTranslationDto createTranslationDto(Long moduleId, String module, String code, String newValue, String defaultValue, Locale locale) {
        return I18NTranslationDto.builder()
            .id(1L)
            .code(code)
            .newValue(newValue)
            .defaultValue(defaultValue)
            .locale(locale)
            .i18NModuleId(moduleId)
            .build();
    }

    private I18NModule createModule(Long id, String name) {
        var module = new I18NModule();
        module.setId(id);
        module.setName(name);
        return module;
    }

}
