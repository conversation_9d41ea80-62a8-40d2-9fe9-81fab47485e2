#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#             Equipment damage and repair estimate message
#
#
#
#
#====================================================================
MESSAGE=DESTIM
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM M 9 Date/time/period                          
GIS C 1 General indicator                         
CUX C 9 Currencies                                
RFF C 9 Reference                                 
FTX C 9 Free text                                 
group 1 M 1
  EQD M 1 Equipment details                         
  DIM C 1 Dimensions                                
  IMD C 9 Item description                          
endgroup 1
group 2 C 9
  NAD M 1 Name and address                          
  MOA C 9 Monetary amount                           
  group 3 C 9
    CTA M 1 Contact information                       
    COM C 9 Communication contact                     
  endgroup 3
endgroup 2
group 4 C 1
  TDT M 1 Details of transport                      
  DTM C 9 Date/time/period                          
  group 5 C 9
    LOC M 1 Place/location identification             
    DTM C 9 Date/time/period                          
  endgroup 5
endgroup 4
UNS M 1 Section control                           
group 6 C 9
  DTM M 1 Date/time/period                          
  LOC C 1 Place/location identification             
  NAD C 9 Name and address                          
endgroup 6
group 7 C 9
  RFF M 1 Reference                                 
  DTM C 1 Date/time/period                          
  NAD C 9 Name and address                          
  MOA C 1 Monetary amount                           
endgroup 7
group 8 C 999
  LIN M 1 Line item                                 
  DIM C 1 Dimensions                                
  QTY C 1 Quantity                                  
  FTX C 9 Free text                                 
  group 9 C 1
    DAM M 1 Damage                                    
    COD C 1 Component details                         
  endgroup 9
  group 10 C 9
    RTE M 1 Rate details                              
    QTY M 1 Quantity                                  
  endgroup 10
  group 11 C 9
    NAD M 1 Name and address                          
    group 12 M 9
      MOA M 1 Monetary amount                           
      TAX C 1 Duty/tax/fee details                      
      MEA C 1 Measurements                              
    endgroup 12
  endgroup 11
endgroup 8
CNT C 9 Control total                             
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Equipment damage and repair
estimate message is DESTIM.
Note: Equipment damage and repair estimate messages conforming
to this document must contain the following data in segment
UNH, composite S009:
Data element  0065 DESTIM
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment to indicate the beginning of a message and to
transmit identifying number and type of message.
![DTM]
DTM, Date/time/period
A segment to indicate date and time of the event being reported
or requested by the message.
![GIS]
GIS, General indicator
A segment to indicate whether the container is damaged and not
available for leasing or still available for leasing (i.e.
without damages that require repair).
![CUX]
CUX, Currencies
A segment to specify various currencies associated with the
message such as labor currency, material currency and Damage
Protection Plan currency.
![RFF]
RFF, Reference
A segment indicating a reference number applicable to the
entire estimate, such as customer reference, lease number, etc.
![FTX]
FTX, Free text
A segment to specify free form or processable supplementary
information (such as coverage of equipment under an Approved
Continuous Examination Program [ACEP] complying with the
International Convention for Safe Containers [CSC]) that
applies to the entire message.
![g1]
Segment group 1:  EQD-DIM-IMD
A group of segments to identify equipment-related data,
including equipment number, type, size, etc.
![EQD 1]
EQD, Equipment details
A segment to specify equipment-related data, including
equipment number, type, size, etc.
![DIM 1]
DIM, Dimensions
A segment indicating the actual dimensions of the equipment,
if the size and type is not known.
![IMD 1]
IMD, Item description
A segment to specify material of which the equipment is and
other specialized data (such as maximum gross weight, tare
weight, etc.).
![g2]
Segment group 2:  NAD-MOA-SG3
A group of segments to identify parties, such as owners,
lessors, users, lessees, equipment depots, agents, etc.,
sending or receiving the estimate. The group also summarizes
responsibility for repair costs by party, with a detailed
enumeration and allocation of such costs, including labor, tax
on labor, materials, tax on materials, subtotal labor and
materials, subtotal tax, and grand totals for the estimate.
![NAD 2]
NAD, Name and address
A segment to identify the name, address, and function of
parties to whom the estimate is sent.
![MOA 2]
MOA, Monetary amount
A segment used to indicate labor, materials, and tax cost
attributable to a party pertaining to the entire estimate.
![g3]
Segment group 3:  CTA-COM
A group of segments to identify individuals or departments
to whose attention the estimate will be directed.
![CTA 3]
CTA, Contact information
A segment to identify a person or department to whom
communications should be directed.
![COM 3]
COM, Communication contact
A segment to identify telephone, fax, etc. numbers at
which the person or department to receive the estimate
may be contacted.
![g4]
Segment group 4:  TDT-DTM-SG5
A group of segments identifying details of the transport, if
any, such as carrier, mode and means of transport, locations
and related date(s) and time(s).
![TDT 4]
TDT, Details of transport
A segment to specify details of transport such as carrier,
the means of transport, voyage number etc.
![DTM 4]
DTM, Date/time/period
A segment to specify date(s) and time(s) associated with the
transport details.
![g5]
Segment group 5:  LOC-DTM
A group of segments to specify any location related to the
transport details.
![LOC 5]
LOC, Place/location identification
A segment to specify any locations related to the
transport details such as ports, terminals, etc.
![DTM 5]
DTM, Date/time/period
A segment to specify date(s) and time(s) related to a
location.
![UNS]
UNS, Section control
A segment to avoid segment collision.
![g6]
Segment group 6:  DTM-LOC-NAD
A group of segments identifying dates and locations of current
and previous inspections, and may include the date of the next
safety inspection due as required under the International
Convention for Safe Containers (CSC) or the U.S. Federal
Highway Administration (FHWA), if indicated on the equipment.
It may also indicate the date and location of manufacture, and
name of manufacturer.
![DTM 6]
DTM, Date/time/period
A segment to specify the date and time of the current or
previous inspection, or of manufacture.
![LOC 6]
LOC, Place/location identification
A segment to specify the location at which the event
described in the previous DTM occurred.
![NAD 6]
NAD, Name and address
A segment to indicate the name and address of the inspection
company, manufacturer, etc. relating to the event described
in the previous DTM.
![g7]
Segment group 7:  RFF-DTM-NAD-MOA
A group of segments indicating information relating to
authorization for repair to proceed and acceptance of
responsibility for respective charges for such repairs. The
approval number, date, and name and address of the approving
party, and the monetary amount authorized are included within
the group.
![RFF 7]
RFF, Reference
A segment indicating the authorization number attributable
to all or part of the estimate, which is provided by the
party.
![DTM 7]
DTM, Date/time/period
A segment indicating the date and time at which the
authorization is provided.
![NAD 7]
NAD, Name and address
A segment indicating the name and address of the authorizing
party.
![MOA 7]
MOA, Monetary amount
A segment indicating the amount of money authorized to be
expended to the account of the authorizing party.
![g8]
Segment group 8:  LIN-DIM-QTY-FTX-SG9-SG10-SG11
A group of segments indicating the type and amount of damage
found on the equipment during an inspection, and a description
of, and allocation of responsibility for, amount, type, extent,
and estimated cost of repairs needed to rectify such damage.
![LIN 8]
LIN, Line item
A segment indicating a number for each damage line-item in
the message. The line number corresponding to a damage and
repair will remain constant throughout all versions of the
message, so that subsequent versions of the repair estimate
may be compared to previous versions. Items to be deleted or
altered in subsequent versions may also be highlighted in
this segment.
![DIM 8]
DIM, Dimensions
A segment used to indicate the dimensions of the repaired
area.
![QTY 8]
QTY, Quantity
A segment used to indicate the number of repairs of the
identical type to be performed.
![FTX 8]
FTX, Free text
A segment to specify free form or processable supplementary
information (such as an indication of conjunctive or
alternate repair) that apply to a single damage and repair
line item.
![g9]
Segment group 9:  DAM-COD
A group of segments describing damages, involved components
(including material for such components), and action to be
taken due to presence of the damage (such as repair).
![DAM 9]
DAM, Damage
A segment to specify equipment damages, such as location,
type, and size of damage; and repair or other action
taken.
![COD 9]
COD, Component details
A segment to specify components involved in the repair,
by type of material.
![g10]
Segment group 10: RTE-QTY
A group of segments indicating the labor rate per hour (or
other unit time), and the number of hours (or time units)
needed to perform the repair.
![RTE 10]
RTE, Rate details
A segment indicating the labor rate per unit of time
(e.g. hour) at which the repair will be charged.
![QTY 10]
QTY, Quantity
A segment indicating the number of time units (e.g.
hours) of labor to be charged for the repair.
![g11]
Segment group 11: NAD-SG12
A group of segments indicating parties responsible for the
repair described, and the allocation of charges, broken down
category, applicable to such repairs, including any tax or
discount.
![NAD 11]
NAD, Name and address
A segment indicating the name and address of the party to
whose account the repair will be charged.
![g12]
Segment group 12: MOA-TAX-MEA
A group of segments indicating the monetary amount of
charges for, the repair, and any tax and or discounts and
surcharges associated with labor and or materials charges
for the repair.
![MOA 12]
MOA, Monetary amount
A segment indicating the monetary amount of labour,
materials, or tax applicable to the repair for which
the indicated party is responsible.
![TAX 12]
TAX, Duty/tax/fee details
A segment indicating the rate of tax applicable to the
associated labor or materials charges.
![MEA 12]
MEA, Measurements
A segment indicating a multiplier associated with
charges for labor or materials. If a discount applies,
the multiplier will be less than one; if a surcharge
applies, the multiplier will be greater than one.
![CNT]
CNT, Control total
A segment to provide message control totals.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
