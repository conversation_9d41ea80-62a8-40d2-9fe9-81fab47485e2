#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                  Insurance premium payment message
#
#
#
#
#====================================================================
MESSAGE=PRPAID
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM C 1 Beginning of message                      
GIS M 1 General indicator                         
RFF M 9 Reference                                 
DTM M 9 Date/time/period                          
ICD C 1 Insurance cover description               
group 1 M 9
  NAD M 1 Name and address                          
  CTA C 1 Contact information                       
  COM C 3 Communication contact                     
  RFF C 9 Reference                                 
endgroup 1
group 2 C 9999
  DOC M 1 Document/message details                  
  group 3 C 1
    GIS M 1 General indicator                         
    FTX C 1 Free text                                 
  endgroup 3
  DTM C 9 Date/time/period                          
  NAD C 9 Name and address                          
  group 4 C 99
    MOA M 1 Monetary amount                           
    DTM C 1 Date/time/period                          
    PCD C 1 Percentage details                        
  endgroup 4
  RFF M 9 Reference                                 
  group 5 C 99
    ICD M 1 Insurance cover description               
    MOA C 99 Monetary amount                           
    RFF C 9 Reference                                 
    PCD C 1 Percentage details                        
  endgroup 5
  group 6 C 9
    ATT M 1 Attribute                                 
    PCD C 1 Percentage details                        
  endgroup 6
  CUX C 1 Currencies                                
endgroup 2
UNS M 1 Section control                           
group 7 C 99
  MOA M 1 Monetary amount                           
  GIS C 1 General indicator                         
  DTM C 1 Date/time/period                          
  RFF C 1 Reference                                 
  ATT C 1 Attribute                                 
  ICD C 1 Insurance cover description               
  CUX C 1 Currencies                                
endgroup 7
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Insurance premium payment message
is PRPAID.
Note: Insurance premium payment messages conforming to this
document must contain the following data in segment UNH,
composite S009:
Data element  0065 PRPAID
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment for specifying message name and function.
![GIS]
GIS, General indicator
A segment containing the indication of the type of the
Insurance Premium Payment message.
![RFF]
RFF, Reference
A segment containing identifying numbers of the Insurance
Premium Payment message or of the statement or the bordereau.
![DTM]
DTM, Date/time/period
A segment specifying dates related to the Insurance Premium
Payment message or to the statement or the bordereau.
![ICD]
ICD, Insurance cover description
The main class of business for premiums or claims within the
message.
![g1]
Segment group 1:  NAD-CTA-COM-RFF
A group of segments providing identification, names and
addresses, contacts and references of the parties involved in
the exchange of the message.
![NAD 1]
NAD, Name and address
A segment for identifying either by names and addresses, or
by codes, the parties and their function in the exchange of
the Insurance Premium Payment message.
![CTA 1]
CTA, Contact information
A segment giving additional contact information of the party
specified in the NAD segment.
![COM 1]
COM, Communication contact
A segment specifying the communication channel and number
for the contact specified in the CTA.
![RFF 1]
RFF, Reference
A segment for additional identifying references of the party
specified in the NAD.
![g2]
Segment group 2:  DOC-SG3-DTM-NAD-SG4-RFF-SG5-SG6-CUX
A group of segments providing detailed information concerning
individual insurance premiums.
![DOC 2]
DOC, Document/message details
A segment providing a unique identification of an Insurance
Premium message or a statement line.
![g3]
Segment group 3:  GIS-FTX
A group of segments providing the paid/partly paid/not paid
indicator and the reason for partly or non payment.
![GIS 3]
GIS, General indicator
A segment providing the paid/partly paid/not paid
indicator.
![FTX 3]
FTX, Free text
A segment providing the reason for partly or non payment.
![DTM 2]
DTM, Date/time/period
A segment specifying dates related to the insurance premium.
![NAD 2]
NAD, Name and address
A segment specifying the policy holder or the insured.
![g4]
Segment group 4:  MOA-DTM-PCD
A group of segments providing total amounts of an Insurance
Premium message or of a statement line. These amounts are
detailed amounts for the bordereau or for the statement.
![MOA 4]
MOA, Monetary amount
A segment specifying premiums, taxes, fees, commissions
and other amounts coming from the Insurance Premium
message or from the statement line.
![DTM 4]
DTM, Date/time/period
A segment specifying dates related to the insurance
premium.
![PCD 4]
PCD, Percentage details
A segment specifying the commission rate for commission
amounts.
![RFF 2]
RFF, Reference
A segment specifying references to documents to which the
insurance premium relates.
![g5]
Segment group 5:  ICD-MOA-RFF-PCD
To specify the covers that apply to the bordereau line.
![ICD 5]
ICD, Insurance cover description
A segment specifying the main class of business of the
insurance premium.
![MOA 5]
MOA, Monetary amount
To specify the monetary amount associated with the cover
such as claim amounts.
![RFF 5]
RFF, Reference
A segment to specify references for the cover.
![PCD 5]
PCD, Percentage details
To specify the percentage of co-insurance for the cover.
![g6]
Segment group 6:  ATT-PCD
A group of segments providing details about the collection
of the insurance premium.
![ATT 6]
ATT, Attribute
A segment providing the type of insurance premium, the
collection details and the co-insurance information.
![PCD 6]
PCD, Percentage details
A segment specifying the percentage of the total premium
of a co-insurance contract, covered by this insurance
premium.
![CUX 2]
CUX, Currencies
A segment specifying currencies and relevant details for the
rate of exchange.
![UNS]
UNS, Section control
A mandatory service segment to be placed before the first user
segment in the summary section to avoid segment collision.
![g7]
Segment group 7:  MOA-GIS-DTM-RFF-ATT-ICD-CUX
A group of segments providing total amounts of the bordereau or
of the statement.
![MOA 7]
MOA, Monetary amount
A segment specifying total amounts.
![GIS 7]
GIS, General indicator
A segment specifying the totalization criterion paid/partly
paid/not paid.
![DTM 7]
DTM, Date/time/period
A segment specifying the totalization criterion expressed by
a date.
![RFF 7]
RFF, Reference
A segment specifying a policy number as totalisation
criterion.
![ATT 7]
ATT, Attribute
A segment specifying the type of premium and/or collection
specifications as totalization criterion.
![ICD 7]
ICD, Insurance cover description
A segment specifying the type of insurance as totalization
criterion.
![CUX 7]
CUX, Currencies
A segment specifying the currency as a totalization
criterion.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
