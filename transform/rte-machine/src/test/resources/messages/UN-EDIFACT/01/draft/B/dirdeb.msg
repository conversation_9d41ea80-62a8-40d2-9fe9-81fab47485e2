#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                         Direct debit message
#
#
#
#
#====================================================================
MESSAGE=DIRDEB
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM M 1 Date/time/period                          
BUS C 1 Business function                         
group 1 C 2
  RFF M 1 Reference                                 
  DTM C 1 Date/time/period                          
endgroup 1
group 2 C 5
  FII M 1 Financial institution information         
  CTA C 1 Contact information                       
  COM C 5 Communication contact                     
endgroup 2
group 3 C 3
  NAD M 1 Name and address                          
  CTA C 1 Contact information                       
  COM C 5 Communication contact                     
endgroup 3
group 4 M 9999
  LIN M 1 Line item                                 
  DTM C 1 Date/time/period                          
  RFF C 2 Reference                                 
  BUS C 1 Business function                         
  FCA C 1 Financial charges allocation              
  group 5 C 1
    MOA M 1 Monetary amount                           
    CUX C 1 Currencies                                
    DTM C 2 Date/time/period                          
    RFF C 1 Reference                                 
  endgroup 5
  group 6 M 1
    FII M 1 Financial institution information         
    CTA C 1 Contact information                       
    COM C 5 Communication contact                     
  endgroup 6
  group 7 C 3
    NAD M 1 Name and address                          
    CTA C 1 Contact information                       
    COM C 5 Communication contact                     
  endgroup 7
  group 8 C 1
    INP M 1 Parties and instruction                   
    FTX C 1 Free text                                 
    DTM C 2 Date/time/period                          
  endgroup 8
  group 9 C 10
    GIS M 1 General indicator                         
    MOA C 1 Monetary amount                           
    LOC C 2 Place/location identification             
    NAD C 1 Name and address                          
    RCS C 1 Requirements and conditions               
    FTX C 10 Free text                                 
  endgroup 9
  group 10 C 1
    PRC M 1 Process identification                    
    FTX M 1 Free text                                 
  endgroup 10
  group 11 M 99999
    SEQ M 1 Sequence details                          
    MOA M 1 Monetary amount                           
    DTM C 1 Date/time/period                          
    BUS C 1 Business function                         
    RFF C 3 Reference                                 
    PAI C 1 Payment instructions                      
    FCA C 1 Financial charges allocation              
    group 12 C 3
      FII M 1 Financial institution information         
      CTA C 1 Contact information                       
      COM C 5 Communication contact                     
    endgroup 12
    group 13 C 3
      NAD M 1 Name and address                          
      CTA C 1 Contact information                       
      COM C 5 Communication contact                     
    endgroup 13
    group 14 C 3
      INP M 1 Parties and instruction                   
      FTX C 1 Free text                                 
      DTM C 2 Date/time/period                          
    endgroup 14
    group 15 C 10
      GIS M 1 General indicator                         
      MOA C 1 Monetary amount                           
      LOC C 2 Place/location identification             
      NAD C 1 Name and address                          
      RCS C 1 Requirements and conditions               
      FTX C 10 Free text                                 
    endgroup 15
    group 16 C 1
      PRC M 1 Process identification                    
      FTX C 5 Free text                                 
      group 17 C 9999
        DOC M 1 Document/message details                  
        MOA C 5 Monetary amount                           
        DTM C 5 Date/time/period                          
        RFF C 5 Reference                                 
        NAD C 2 Name and address                          
        group 18 C 5
          CUX M 1 Currencies                                
          DTM C 1 Date/time/period                          
        endgroup 18
        group 19 C 100
          AJT M 1 Adjustment details                        
          MOA M 1 Monetary amount                           
          RFF C 1 Reference                                 
          FTX C 5 Free text                                 
        endgroup 19
        group 20 C 1000
          DLI M 1 Document line identification              
          MOA M 5 Monetary amount                           
          PIA C 5 Additional product id                     
          DTM C 5 Date/time/period                          
          group 21 C 5
            CUX M 1 Currencies                                
            DTM C 1 Date/time/period                          
          endgroup 21
          group 22 C 10
            AJT M 1 Adjustment details                        
            MOA M 1 Monetary amount                           
            RFF C 1 Reference                                 
            FTX C 5 Free text                                 
          endgroup 22
        endgroup 20
      endgroup 17
      group 23 C 1
        GIS M 1 General indicator                         
        MOA C 5 Monetary amount                           
      endgroup 23
    endgroup 16
  endgroup 11
endgroup 4
CNT C 5 Control total                             
group 24 C 5
  AUT M 1 Authentication result                     
  DTM C 1 Date/time/period                          
endgroup 24
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Direct debit message is DIRDEB.
Note: Direct debit messages conforming to this document must
contain the following data in segment UNH, composite S009:
Data element  0065 DIRDEB
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment by means of which the sender must uniquely identify
the DIRDEB message using its type (e.g., kind of pre-authorized
instructions or not pre-authorized) and number and its
function. The requirement for a response, e.g., related credit
advices, may be indicated.

Note: The identification of the DIRDEB will be passed back to
the creditor for reconciliation purposes.
![DTM]
DTM, Date/time/period
A segment specifying the date and/or time the message is
created.
![BUS]
BUS, Business function
A segment identifying certain characteristics of the DIRDEB,
such as, its business function. In so doing, it provides
information about the message that may be used to route the
message within an institution, for tariffing, or for the
provision of some statistical information.
![g1]
Segment group 1:  RFF-DTM
A group of segments identifying a previously sent message.
![RFF 1]
RFF, Reference
A segment identifying a previously sent message.
![DTM 1]
DTM, Date/time/period
A segment identifying the date/time of the previously sent
message.
![g2]
Segment group 2:  FII-CTA-COM
A group of segments identifying the financial institutions
involved in the direct debit, their functions and their
contacts.
![FII 2]
FII, Financial institution information
A segment identifying the financial institution associated
with the transaction and their functions.
![CTA 2]
CTA, Contact information
A segment identifying a person or a department for the party
specified in the leading FII segment to whom communication
should be directed.
![COM 2]
COM, Communication contact
A segment identifying communication type(s) and number(s) of
person(s) or department(s) specified in the associated CTA
segment.
![g3]
Segment group 3:  NAD-CTA-COM
A group of segments identifying the name(s) and address(es) of
each non-financial party involved in the transaction, their
functions and their contacts.
![NAD 3]
NAD, Name and address
A segment identifying names and addresses of the parties
associated with the direct debit, in coded or uncoded form,
and their functions.
![CTA 3]
CTA, Contact information
A segment identifying a person or a department for the party
specified in the leading NAD segment and to whom
communication should be directed.
![COM 3]
COM, Communication contact
A segment identifying communication type(s) and number(s) of
person(s) or department(s) specified in the associated CTA
segment.
![g4]
Segment group 4:  LIN-DTM-RFF-BUS-FCA-SG5-SG6-SG7-SG8-SG9-SG10-
                  SG11
A group of segments containing information regarding the credit
side of the transaction and details which are pertinent to all
direct debits specified within the group. Certain payment
details may be provided either in segment group 4 or in segment
group 11, but not in both.
![LIN 4]
LIN, Line item
A segment identifying the beginning of the credit side of
the transaction (B level) by a sequential line number, which
may also be used for reconciliation purposes.
![DTM 4]
DTM, Date/time/period
A segment identifying the date, at which an order has been
requested to be executed or acted upon.
![RFF 4]
RFF, Reference
A segment specifying reference of the single credit amount
on the credit side of the transaction (B level).
Note: The identification will be passed back to the creditor
for reconciliation purposes.
![BUS 4]
BUS, Business function
A segment identifying certain characteristics of the direct
debits stored on the debit side of the transaction (C
level), such as business function.
![FCA 4]
FCA, Financial charges allocation
A segment specifying the method for allocation of charges
and allowances and identifying the creditor's account to
which such charges or allowances should be directed where it
is different from the principal account. This constitutes a
default specification and is mutually exclusive with the FCA
segment in Segment Group 11.
![g5]
Segment group 5:  MOA-CUX-DTM-RFF
A group of segments containing the currency of the single
amounts on the debit side of the transaction (C level),
currency to be collected, plus the total of all single
amounts of the debit side of the transaction (C level).
![MOA 5]
MOA, Monetary amount
A segment specifying the total amount and the currency to
be collected.
![CUX 5]
CUX, Currencies
A segment specifying the source currency and the target
currency of the transaction, when they are different. The
rate of exchange is solely used when previously agreed
between the creditor and the creditor's bank.
![DTM 5]
DTM, Date/time/period
A segment identifying the effective date and/or time the
rate of exchange was fixed. The other occurrence
identifies the reference date.
![RFF 5]
RFF, Reference
A segment identifying other transactions to which funds
associated with the direct debit are related such as
separate foreign exchange deal.
![g6]
Segment group 6:  FII-CTA-COM
A group of segments containing the account servicing bank
including the account to be credited. If the creditor is not
quoted separately in the NAD segment on the credit side of
the transaction (B level), then this group also contains the
information of the creditor.
![FII 6]
FII, Financial institution information
A segment identifying the financial institution and
relevant account number associated with the transaction,
and their functions.
![CTA 6]
CTA, Contact information
A segment identifying a person or a department for the
party specified in the leading FII segment to whom
communication should be directed.
![COM 6]
COM, Communication contact
A segment identifying communication type(s) and number(s)
of person(s) or department(s) specified in the associated
CTA segment.
![g7]
Segment group 7:  NAD-CTA-COM
A group of segments identifying the creditor. Furthermore,
one contact party can also be quoted in this group.
![NAD 7]
NAD, Name and address
A segment identifying name and address of the non-
financial institutions associated with the credit side of
the transaction (B level).
![CTA 7]
CTA, Contact information
A segment identifying a person or a department for the
party specified in the leading NAD segment to whom
communication should be directed.
![COM 7]
COM, Communication contact
A segment identifying communication type(s) and number(s)
of person(s) or department(s) specified in the associated
CTA segment.
![g8]
Segment group 8:  INP-FTX-DTM
A group of segments containing instructions from the
creditor relating to parties identified in the NAD and FII
segments. It specifies action to be taken by the identified
parties, and the date (and optionally time) by which such
action needs to be taken.
![INP 8]
INP, Parties and instruction
A segment identifying the party to enact the instruction
and the parties to be contacted. It specifies where
appropriate the instruction in coded form.
![FTX 8]
FTX, Free text
A segment providing free text instruction relating to the
associated INP segment.
![DTM 8]
DTM, Date/time/period
A segment specifying the earliest and latest dates and/or
times by which the instruction specified in the INP
and/or FTX segment(s) needs to be carried out.
![g9]
Segment group 9:  GIS-MOA-LOC-NAD-RCS-FTX
A group of segments providing information for subsequent use
by regulatory authorities requiring statistical and other
types of data. It also identifies the regulatory authority
for which the information is intended followed by the
information itself. 
This group is mutually exclusive with Segment Group 15.
![GIS 9]
GIS, General indicator
A segment identifying what processing should be completed
for regulatory authorities.
![MOA 9]
MOA, Monetary amount
A segment giving the amount and the currency of each
transaction to be reported.
![LOC 9]
LOC, Place/location identification
A segment giving the different origins/destinations
(places) of goods/investment/services.
![NAD 9]
NAD, Name and address
A segment identifying a party related to the associated
informative text.
![RCS 9]
RCS, Requirements and conditions
A segment giving the nature (e.g. goods, transport
services) and direction of each transaction to be
reported in coded form.
![FTX 9]
FTX, Free text
A segment giving information in coded or in clear form to
provide information relevant to regulatory authorities
requirements.
![g10]
Segment group 10: PRC-FTX
A group of segments giving information in free form about
the purpose of the direct debit from the creditor to the
debtors.
![PRC 10]
PRC, Process identification
A segment identifying the kind of process at the debtor's
side.
![FTX 10]
FTX, Free text
A segment providing clear form information from the
creditor to the debtor.
![g11]
Segment group 11: SEQ-MOA-DTM-BUS-RFF-PAI-FCA-SG12-SG13-
                  SG14-SG15-SG16
This Segment Group contains information regarding the
debtor(s) of the direct debit. Certain payment details may
be provided either in Segment Group 11 or in Segment Group
4, but not in both.
![SEQ 11]
SEQ, Sequence details
A segment identifying the beginning of the debit side of
the transaction (C level) by a sequential number, which
may also be used for reconciliation purposes.
![MOA 11]
MOA, Monetary amount
A segment giving the amount of the direct debit. It is
the amount to be collected. The currency is quoted on the
credit side of the transaction (B level).
![DTM 11]
DTM, Date/time/period
A segment identifying date(s)/time(s) to the debtor's
side.
![BUS 11]
BUS, Business function
A segment identifying business information for the direct
debit.
![RFF 11]
RFF, Reference
A segment identifying a transaction from the creditor to
the debtor and/or from the creditor to the creditor's
bank.
![PAI 11]
PAI, Payment instructions
A segment specifying the conditions, method and channel
of payment for the direct debit.
![FCA 11]
FCA, Financial charges allocation
A segment specifying the method for allocation of charges
and allowances and identifying the creditor's account to
which such charges or allowances should be directed where
it is different from the principal account. This segment
is mutually exclusive with the FCA segment in Segment
Group 4.
![g12]
Segment group 12: FII-CTA-COM
A group of segments containing the debtor's bank and the
account and if necessary up to two intermediary banks.
![FII 12]
FII, Financial institution information
A segment identifying the financial institution and
relevant account number associated with the
transaction, and their functions.
![CTA 12]
CTA, Contact information
A segment identifying a person or a department for the
party specified in the leading FII segment to whom
communication should be directed.
![COM 12]
COM, Communication contact
A segment identifying communication type(s) and
number(s) of person(s) or department(s) specified in
the associated CTA segment.
![g13]
Segment group 13: NAD-CTA-COM
A group of segments identifying the name and address of
the debtor and one contact party.
![NAD 13]
NAD, Name and address
A segment identifying names and addresses of the non-
financial institutions associated with the direct
debit on the debtor's side .
![CTA 13]
CTA, Contact information
A segment identifying a person or a department for the
party specified in the leading NAD segment to whom
communication should be directed.
![COM 13]
COM, Communication contact
A segment identifying communication type(s) and
number(s) of person(s) or department(s) specified in
the associated CTA segment.
![g14]
Segment group 14: INP-FTX-DTM
A group of segments containing instructions from the
creditor relating to parties identified in the NAD and
FII segments. It specifies action to be taken by the
identified parties, and the date (and optionally time) by
which such actions needs to be taken.
![INP 14]
INP, Parties and instruction
A segment identifying the party to enact the
instruction and the parties to be contacted. It
specifies where appropriate the instruction in coded
form.
![FTX 14]
FTX, Free text
A segment providing free text instruction relating to
the associated INP segment.
![DTM 14]
DTM, Date/time/period
A segment specifying the earliest and latest dates
and/or times by which the instruction specified in the
INP and/or FTX segment(s) needs to be carried out.
![g15]
Segment group 15: GIS-MOA-LOC-NAD-RCS-FTX
A group of segments providing information for subsequent
use by regulatory authorities requiring statistical and
other types of data. It also identifies the regulatory
authority for which the information is intended followed
by the information itself.
![GIS 15]
GIS, General indicator
A segment identifying what processing should be
completed for regulatory authorities.
![MOA 15]
MOA, Monetary amount
A segment giving the amount and the currency of each
transaction to be reported.
![LOC 15]
LOC, Place/location identification
A segment giving the different origins/destinations
(places) of goods/investment/services.
![NAD 15]
NAD, Name and address
A segment identifying a party related to the
associated informative text.
![RCS 15]
RCS, Requirements and conditions
A segment giving the nature (e.g. goods, transport
services) and direction of each transaction to be
reported in coded form.
![FTX 15]
FTX, Free text
A segment giving information in coded or in clear form
to provide information relevant to regulatory
authorities requirements.
![g16]
Segment group 16: PRC-FTX-SG17-SG23
A group of segments containing the details of the direct
debit. The content of these segments is passed through
the banking chain from the creditor to the debtor.
![PRC 16]
PRC, Process identification
A segment identifying the kind of processing at the
debtor's side.
![FTX 16]
FTX, Free text
A segment providing free text instruction(s) relating
to the direct debit details.
![g17]
Segment group 17: DOC-MOA-DTM-RFF-NAD-SG18-SG19-SG20
A group of segments providing details of all documents
e.g., invoices, statements, despatch advices, etc., to
which the direct debit refers. It includes information
on the monetary amounts for each document and on any
adjustments (with an indication of the reason for
adjustments) and discounts. For information purposes
an indication of the tax element can be provided.
![DOC 17]
DOC, Document/message details
A segment identifying the reference document
against which payment is being made.
![MOA 17]
MOA, Monetary amount
A segment giving the monetary amounts of each
reference document (e.g., original amount, discount
amount etc.). The amount due and the amount
remitted are mandatory.
![DTM 17]
DTM, Date/time/period
A segment specifying the date of the referenced
document and indicating any other relevant dates
applicable.
![RFF 17]
RFF, Reference
A segment for the inclusion of any additional
references related to the reference document.
![NAD 17]
NAD, Name and address
A segment identifying a party name and address,
either by coded identification or in a clear form.
![g18]
Segment group 18: CUX-DTM
A group of segments specifying the currencies and
the related dates/periods valid to the referenced
document where different to the reference currency.
![CUX 18]
CUX, Currencies
A segment identifying the currency and
associated exchange rate of the referenced
document where different to the remittance
currency.
![DTM 18]
DTM, Date/time/period
A segment specifying the date/time/period
related to the rate of exchange.
![g19]
Segment group 19: AJT-MOA-RFF-FTX
A group of segments indicating adjustment amounts
and their referenced documents.
![AJT 19]
AJT, Adjustment details
A segment indicating any adjustments to the
amounts originally specified in the referenced
document, and to which items such adjustments
apply, with the associated reason for
adjustment.
![MOA 19]
MOA, Monetary amount
A segment giving the monetary amounts of the
adjustments of each reference document (e.g.
original amounts, discount amount, etc.). The
currency data elements should not be used.
![RFF 19]
RFF, Reference
A segment for the inclusion of any additional
references related to the reference documents.
![FTX 19]
FTX, Free text
A segment providing free text information
related to the payment details.
![g20]
Segment group 20: DLI-MOA-PIA-DTM-SG21-SG22
A group of segments which may be used when required
to provide details of individual line items in the
reference document.
![DLI 20]
DLI, Document line identification
A segment identifying a specific line item
within the reference document.
![MOA 20]
MOA, Monetary amount
A segment giving the monetary amounts for this
line item.
![PIA 20]
PIA, Additional product id
A segment specifying item identification codes
where required.
![DTM 20]
DTM, Date/time/period
A segment specifying the date/time/period
related to the line item.
![g21]
Segment group 21: CUX-DTM
A group of segments specifying the currencies
and the related dates/periods valid to the
referenced document where different to the
reference currency.
![CUX 21]
CUX, Currencies
A segment identifying the currency and
associated exchange rate of the referenced
document where different to the remittance
currency.
![DTM 21]
DTM, Date/time/period
A segment specifying the date/time/period
related to the rate of exchange.
![g22]
Segment group 22: AJT-MOA-RFF-FTX
A group of segments indicating adjustment
amounts and their reference documents.
![AJT 22]
AJT, Adjustment details
A segment indicating any adjustments to the
amounts originally specified in the
referenced document, and to which items such
adjustments apply with the associated reason
for adjustment.
![MOA 22]
MOA, Monetary amount
A segment giving the monetary amounts of the
adjustments of each reference document (e.g.
original amounts, discount amounts, etc.).
The currency data elements should not be
used.
![RFF 22]
RFF, Reference
A segment for the inclusion of any additional
references related to the reference document.
![FTX 22]
FTX, Free text
A segment providing free text information
related to the payment details.
![g23]
Segment group 23: GIS-MOA
A group of segments indicating the end of the details
of payment and specifying hash total amounts for
control purposes.
![GIS 23]
GIS, General indicator
A segment identifying the end of the details of
payment.
![MOA 23]
MOA, Monetary amount
A segment indicating total amounts for control
purposes.
![CNT]
CNT, Control total
A segment providing information on control totals.
![g24]
Segment group 24: AUT-DTM
A group of segments specifying details of any authentication
(validation) procedures applied to the DIRDEB message.
![AUT 24]
AUT, Authentication result
A segment specifying the details of any authentication
(validation) procedures applied to the DIRDEB message.
![DTM 24]
DTM, Date/time/period
A segment identifying the validation date/time.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
