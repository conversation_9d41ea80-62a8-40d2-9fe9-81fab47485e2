#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                  Documentary credit advice message
#
#
#
#
#====================================================================
MESSAGE=DOCADV
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
RFF M 1 Reference                                 
BUS M 1 Business function                         
INP M 10 Parties and instruction                   
FCA M 3 Financial charges allocation              
DTM M 3 Date/time/period                          
FTX C 20 Free text                                 
group 1 M 9
  FII M 1 Financial institution information         
  RFF C 2 Reference                                 
  CTA C 1 Contact information                       
  COM C 5 Communication contact                     
endgroup 1
group 2 M 9
  NAD M 1 Name and address                          
  RFF C 1 Reference                                 
  CTA C 1 Contact information                       
  COM C 5 Communication contact                     
endgroup 2
group 3 M 1
  DTM M 1 Date/time/period                          
  LOC M 1 Place/location identification             
endgroup 3
group 4 M 5
  MOA M 1 Monetary amount                           
  group 5 C 1
    ALC M 1 Allowance or charge                       
    PCD C 2 Percentage details                        
  endgroup 5
endgroup 4
group 6 M 3
  LOC M 1 Place/location identification             
  DTM C 1 Date/time/period                          
endgroup 6
group 7 M 1
  PAI M 1 Payment instructions                      
  FII C 1 Financial institution information         
  LOC C 1 Place/location identification             
endgroup 7
group 8 M 5
  PAT M 1 Payment terms basis                       
  FII C 1 Financial institution information         
  DTM C 1 Date/time/period                          
  MOA C 1 Monetary amount                           
  PCD C 1 Percentage details                        
  FTX C 1 Free text                                 
endgroup 8
group 9 C 1
  TOD M 1 Terms of delivery or transport            
  LOC C 1 Place/location identification             
endgroup 9
group 10 C 1
  TSR M 1 Transport service requirements            
  LOC C 5 Place/location identification             
endgroup 10
group 11 C 5
  INP M 1 Parties and instruction                   
  FTX C 1 Free text                                 
  DTM C 2 Date/time/period                          
endgroup 11
group 12 M 9
  RFF M 1 Reference                                 
  DTM C 1 Date/time/period                          
  FTX C 2 Free text                                 
endgroup 12
group 13 M 20
  DOC M 1 Document/message details                  
  MOA C 1 Monetary amount                           
  PCD C 1 Percentage details                        
  LOC C 1 Place/location identification             
  FTX C 1 Free text                                 
  group 14 C 20
    ICD M 1 Insurance cover description               
    DTM C 1 Date/time/period                          
    FTX C 9 Free text                                 
  endgroup 14
  group 15 C 9
    ALI M 1 Additional information                    
    group 16 C 3
      NAD M 1 Name and address                          
      CTA C 1 Contact information                       
      COM C 5 Communication contact                     
    endgroup 16
  endgroup 15
endgroup 13
group 17 C 1
  AUT M 1 Authentication result                     
  DTM C 1 Date/time/period                          
endgroup 17
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Documentary credit advice message
is DOCADV.
Note: Documentary credit advice messages conforming to this
document must contain the following data in segment UNH,
composite S009:
Data element  0065 DOCADV
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment by means of which the sender must uniquely identify
the DOCADV message using its type and number and when necessary
its function.
![RFF]
RFF, Reference
A segment identifying the documentary credit number.
![BUS]
BUS, Business function
A segment providing information related to the type of
documentary credit (i.e. irrevocable or revocable, transferable
or not transferable). It may also identify the geographic
environment of the documentary credit.
![INP]
INP, Parties and instruction
A segment specifying special documentary credit instructions.
It specifies the confirmation instructions and, if applicable,
any additional conditions, the method of issuance of the
documentary credit, pre-advice instructions and documents
delivery instructions.
![FCA]
FCA, Financial charges allocation
A segment specifying the allocation of charges.
![DTM]
DTM, Date/time/period
A segment specifying the date of issuance of the documentary
credit, the date and/or time the message is created and, if
applicable, the presentation period within which documents are
to be presented.
![FTX]
FTX, Free text
A segment with free text information used when additional
information is needed but cannot be accommodated within other
segments. This may be instructions and information about
revolving documentary credits and other supplementary
information.
![g1]
Segment group 1:  FII-RFF-CTA-COM
A group of segments identifying the financial institutions and
account numbers involved in the documentary credit, their
functions and notification references.
![FII 1]
FII, Financial institution information
A segment identifying the financial institution(s)
associated with the documentary credit, in coded or uncoded
form, their functions and account numbers involved.
![RFF 1]
RFF, Reference
A segment specifying reference numbers of parties specified
in the leading FII segment.
![CTA 1]
CTA, Contact information
A segment identifying a person or a department for the party
specified in the leading FII segment to whom communication
should be directed.
![COM 1]
COM, Communication contact
A segment identifying communication type(s) and number(s) of
person(s) or department(s) specified in the associated CTA
segment.
![g2]
Segment group 2:  NAD-RFF-CTA-COM
A group of segments identifying the name and address of each
non-financial party involved in the transaction, their
functions and notification references.
![NAD 2]
NAD, Name and address
A segment identifying names and addresses of the parties
associated with the documentary credit, in coded or uncoded
form, and their functions.
![RFF 2]
RFF, Reference
A segment specifying reference numbers of parties specified
in the leading NAD segment.
![CTA 2]
CTA, Contact information
A segment identifying a person or a department for the party
specified in the leading NAD segment to whom communication
should be directed.
![COM 2]
COM, Communication contact
A segment identifying communication type(s) and number(s) of
person(s) or department(s) specified in the associated CTA
segment.
![g3]
Segment group 3:  DTM-LOC
A group of segments specifying the expiry date of the
documentary credit and the place where the documentary credit
expires for the presentation of the required documents.
![DTM 3]
DTM, Date/time/period
A segment specifying the expiry date of the documentary
credit.
![LOC 3]
LOC, Place/location identification
A segment specifying the place where the documentary credit
expires for the presentation of the required documents.
![g4]
Segment group 4:  MOA-SG5
A group of segments specifying the amount and currency of the
documentary credit and, if applicable, any additional amounts
covered under the documentary credit. It may also specify the
tolerance or any details of the amount(s).
![MOA 4]
MOA, Monetary amount
A segment specifying the amount and currency of the
documentary credit and, if applicable, any additional
amounts covered under the documentary credit.

Specification of the documentary credit amount is mandatory
for the DOCADV message.
![g5]
Segment group 5:  ALC-PCD
A group of segments specifying the tolerance or
specification of the amount(s) specified in Segment Group 4.
![ALC 5]
ALC, Allowance or charge
A segment specifying the tolerance or specification of
the amount(s) specified in Segment Group 4.
![PCD 5]
PCD, Percentage details
A segment specifying the amount tolerance in percentage.
![g6]
Segment group 6:  LOC-DTM
A group of segments specifying from where and to where the
shipment(s)under the documentary credit must be made and
relevant dates for the transport of goods.
![LOC 6]
LOC, Place/location identification
A segment specifying from where and to where shipment(s)
under the documentary credit must be made.
![DTM 6]
DTM, Date/time/period
A segment specifying the date or period of shipment.
![g7]
Segment group 7:  PAI-FII-LOC
A group of segments specifying with whom the documentary credit
is available.
![PAI 7]
PAI, Payment instructions
A segment specifying with whom the documentary credit is
available.
![FII 7]
FII, Financial institution information
A segment identifying, when necessary, the named bank with
whom the documentary credit is available in coded or uncoded
form.
![LOC 7]
LOC, Place/location identification
A segment identifying the city and/or country where the
documentary credit is available with any bank.
![g8]
Segment group 8:  PAT-FII-DTM-MOA-PCD-FTX
A group of segments specifying the method of availability of
the documentary credit.
![PAT 8]
PAT, Payment terms basis
A segment specifying the method of availability. It may also
specify at which maturity date payment is to be made and how
the maturity date is to be determined.
![FII 8]
FII, Financial institution information
A segment identifying, when necessary, the party on whom
drafts must be drawn.
![DTM 8]
DTM, Date/time/period
A segment specifying, when necessary, a specific maturity
date.
![MOA 8]
MOA, Monetary amount
A segment specifying in those cases that the documentary
credit should be available by mixed payment (e.g. sight
payment and deferred payment) the part of the documentary
credit amount that is payable by sight payment, deferred
payment, etc.
![PCD 8]
PCD, Percentage details
A segment specifying in those cases that the documentary
credit should be available by mixed payment (e.g. sight
payment and deferred payment) the part of the documentary
credit amount in percentage which is payable by sight
payment, deferred payment, etc.
![FTX 8]
FTX, Free text
A segment specifying how the maturity date is to be
determined or indicate at which usance time draft(s) must be
drawn in uncoded form.
![g9]
Segment group 9:  TOD-LOC
A group of segments indicating the terms of delivery.
![TOD 9]
TOD, Terms of delivery or transport
A segment identifying the terms of delivery.
![LOC 9]
LOC, Place/location identification
A segment identifying locations relevant to the terms of
delivery specified in the leading TOD segment.
![g10]
Segment group 10: TSR-LOC
A group of segments specifying instructions about transhipments
and partial shipments under the documentary credit.
![TSR 10]
TSR, Transport service requirements
A segment specifying whether transhipments and/or partial
shipments are allowed or not.
![LOC 10]
LOC, Place/location identification
A segment identifying a certain city and/or country where
transhipment is allowed or not.
![g11]
Segment group 11: INP-FTX-DTM
A group of segments specifying special instructions and related
information from the Applicant to parties identified in Segment
Group 1 and/or Segment Group 2.
![INP 11]
INP, Parties and instruction
A segment identifying the party which is to carry out the
instruction and the party to be contacted followed by the
instruction in coded form.
![FTX 11]
FTX, Free text
A segment specifying instructions in uncoded form.
![DTM 11]
DTM, Date/time/period
A segment specifying the earliest and latest dates and/or
times by which the specified instructions must be carried
out. It will be repeated when more than one date or time is
to be provided.
![g12]
Segment group 12: RFF-DTM-FTX
A group of segments giving references and where necessary,
their dates, relating to the description of goods or services
(e.g. purchase order, delivery contract) followed by the
description of goods or services in uncoded form.
![RFF 12]
RFF, Reference
A segment specifying the reference number of a purchase
order, delivery contract, etc.
![DTM 12]
DTM, Date/time/period
A segment specifying the date related to the reference
number specified in the leading RFF segment.
![FTX 12]
FTX, Free text
A segment specifying the description of goods or services in
free text form.
![g13]
Segment group 13: DOC-MOA-PCD-LOC-FTX-SG14-SG15
A group of segments specifying the documents required under the
documentary credit and the requirements for each document. It
may also identify party(ies) related to these requirements.
![DOC 13]
DOC, Document/message details
A segment specifying the documents required under the
documentary credit. It may also specify, when necessary, the
number of copies and originals of document required.
![MOA 13]
MOA, Monetary amount
A segment specifying, when necessary, the value of insurance
for an insurance certificate or an insurance policy in
monetary amount and/or the amount of a debit or credit note.
![PCD 13]
PCD, Percentage details
A segment specifying, when necessary, the value of insurance
for an insurance certificate or an insurance policy in
percentage and/or the percentage of a debit or credit note.
![LOC 13]
LOC, Place/location identification
A segment identifying the city and/or country where claims
under an insurance certificate or an insurance policy are
payable.
![FTX 13]
FTX, Free text
A segment specifying the insured risks and/or documentary
requirements in uncoded form.
![g14]
Segment group 14: ICD-DTM-FTX
A segment group specifying the insurance covers required for
an insurance certificate/insurance policy specified in the
DOC segment of segment group 13.
![ICD 14]
ICD, Insurance cover description
A segment specifying the insurance covers required for an
insurance certificate/insurance policy in coded form.
![DTM 14]
DTM, Date/time/period
A segment specifying the version of standard cover clause
specified in the ICD segment.
![FTX 14]
FTX, Free text
A segment specifying the non-standard cover texts or
restrictions or expansions to the standard cover clause.
![g15]
Segment group 15: ALI-SG16
A segment group specifying the documentary requirements in
coded form for documents specified in the DOC segment of
Segment Group 13.
![ALI 15]
ALI, Additional information
A segment specifying the documentary requirements in
coded form and, where necessary, the country of origin.
![g16]
Segment group 16: NAD-CTA-COM
A group of segments identifying party(ies) related to the
ALI segment in Segment Group 15. It may also include
contact information about each party, when necessary.
![NAD 16]
NAD, Name and address
A segment identifying party(ies) related to the ALI
segment in Segment Group 15, in coded or uncoded form,
and their functions.
![CTA 16]
CTA, Contact information
A segment identifying a person or a department for the
party specified in the leading NAD segment to whom
communication should be directed.
![COM 16]
COM, Communication contact
A segment identifying communication type(s) and
number(s) of person(s) or department(s) specified in
the associated CTA segment.
![g17]
Segment group 17: AUT-DTM
A group of segments specifying details of any authentication
(validation) procedures applied to the DOCADV message.
![AUT 17]
AUT, Authentication result
A segment specifying details of any authentication
(validation) procedures applied to the DOCADV message.
![DTM 17]
DTM, Date/time/period
A segment identifying the validation date/time.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
