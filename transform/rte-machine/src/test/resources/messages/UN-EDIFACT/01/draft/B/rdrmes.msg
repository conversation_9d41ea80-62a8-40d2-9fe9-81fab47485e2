#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                      Raw data reporting message
#
#
#
#
#====================================================================
MESSAGE=RDRMES
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM C 5 Date/time/period                          
group 1 C 9
  NAD M 1 Name and address                          
  RFF C 9 Reference                                 
  FTX C 9 Free text                                 
  group 2 C 9
    CTA M 1 Contact information                       
    COM C 9 Communication contact                     
  endgroup 2
endgroup 1
group 3 M 9999
  IDE M 1 Identity                                  
  group 4 C 1
    NAD M 1 Name and address                          
    RFF C 9 Reference                                 
    FTX C 9 Free text                                 
    group 5 C 9
      CTA M 1 Contact information                       
      COM C 9 Communication contact                     
    endgroup 5
  endgroup 4
  group 6 M 99999
    SCD M 1 Structure component definition            
    ARR C 99999 Array information                         
    RFF C 99 Reference                                 
    DTM C 9 Date/time/period                          
    FTX C 99 Free text                                 
    group 7 C 99999
      NAD M 1 Name and address                          
      RFF C 9 Reference                                 
      FTX C 9 Free text                                 
      group 8 C 9
        CTA M 1 Contact information                       
        COM C 9 Communication contact                     
      endgroup 8
    endgroup 7
  endgroup 6
endgroup 3
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Raw data reporting message is
RDRMES.
Note: Raw data reporting messages conforming to this document
must contain the following data in segment UNH, composite S009:
Data element  0065 RDRMES
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment specifying the functional use (e.g. sub-set) of the
Raw data reporting message and identifying the message.
![DTM]
DTM, Date/time/period
A segment identifying dates, times, or periods which are
relevant to the whole message.
![g1]
Segment group 1:  NAD-RFF-FTX-SG2
A group of segments identifying the name and address of the
relevant parties, along with additional information related to
the interchanging partners, such as references, contacts and
communication numbers.
![NAD 1]
NAD, Name and address
A segment to identify the relevant parties.
![RFF 1]
RFF, Reference
A segment to specify a reference that the respondent wishes
to communicate to the collector.
![FTX 1]
FTX, Free text
A segment to provide free form or coded text information.
![g2]
Segment group 2:  CTA-COM
A group of segments identifying a contact within the
identified organization and communication number (e.g.
telephone number, fax number).
![CTA 2]
CTA, Contact information
A segment to identify a person or a department to whom
communication should be directed.
![COM 2]
COM, Communication contact
A segment to identify a communication number of a
department or a person to whom communication should be
directed.
![g3]
Segment group 3:  IDE-SG4-SG6
A group of segments identifying a questionnaire, the responding
organization, the questions in the questionnaire and the
responses to the questions.
![IDE 3]
IDE, Identity
A segment identifying a questionnaire.
![g4]
Segment group 4:  NAD-RFF-FTX-SG5
A group of segments identifying the responding organization
to a questionnaire, the contact details, and administrative
data relevant to the organization.
![NAD 4]
NAD, Name and address
A segment identifying the responding organization.
![RFF 4]
RFF, Reference
A segment to specify a reference that the responding
organization wishes to communicate to the collector.
![FTX 4]
FTX, Free text
A segment to provide free form or coded text information.
![g5]
Segment group 5:  CTA-COM
A group of segments identifying a contact within, or
representing, the responding organization and
communication number (e.g. telephone number, fax number).
![CTA 5]
CTA, Contact information
A segment to identify a person or a department to whom
communication should be directed.
![COM 5]
COM, Communication contact
A segment to identify a communication number of a
department or a person to whom communication should be
directed.
![g6]
Segment group 6:  SCD-ARR-RFF-DTM-FTX-SG7
A group of segments which specify a question in a
questionnaire and the data given in response.
![SCD 6]
SCD, Structure component definition
A segment to specify the identity of a question (e.g. in
a questionnaire).
![ARR 6]
ARR, Array information
A segment containing the data in response to a question.
![RFF 6]
RFF, Reference
A segment to specify a reference relevant to the
response.
![DTM 6]
DTM, Date/time/period
A segment identifying dates, times, or periods relevant
to the response.
![FTX 6]
FTX, Free text
A segment to provide free form or coded text information
relevant to the response.
![g7]
Segment group 7:  NAD-RFF-FTX-SG8
A group of segments that identify an organization,
contact information and administrative information given
in response to a question.
![NAD 7]
NAD, Name and address
A segment specifying the identity of an organization
which is given in response to a question.
![RFF 7]
RFF, Reference
A segment to specify a reference relevant to the
organization.
![FTX 7]
FTX, Free text
A segment to provide free form or coded text
information.
![g8]
Segment group 8:  CTA-COM
A group of segments identifying a contact within the
identified organization and communication number (e.g.
telephone number, fax number).
![CTA 8]
CTA, Contact information
A segment to identify a person or a department to
whom communication should be directed.
![COM 8]
COM, Communication contact
A segment to identify a communication number of a
department or a person to whom communication should
be directed.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
