#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                    Project tasks planning message
#
#
#
#
#====================================================================
MESSAGE=PROTAP
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
GIS M 1 General indicator                         
DTM M 99 Date/time/period                          
group 1 M 99
  RFF M 1 Reference                                 
  IMD C 9 Item description                          
  BII C 99 Structure identification                  
  group 2 C 99
    GIR M 1 Related identification numbers            
    REL C 1 Relationship                              
  endgroup 2
endgroup 1
group 3 M 99
  PNA M 1 Party identification                      
  ADR C 1 Address                                   
  BII C 1 Structure identification                  
  group 4 C 9
    CTA M 1 Contact information                       
    COM C 9 Communication contact                     
  endgroup 4
  group 5 C 9
    CED M 1 Computer environment details              
    DTM C 9 Date/time/period                          
  endgroup 5
endgroup 3
group 6 C 99
  EFI M 1 External file link identification         
  CED C 1 Computer environment details              
  DTM C 1 Date/time/period                          
endgroup 6
group 7 C 999
  IND M 1 Index details                             
  BII C 1 Structure identification                  
  IMD C 1 Item description                          
  RCS C 1 Requirements and conditions               
endgroup 7
group 8 C 9999
  LIN M 1 Line item                                 
  BII C 9 Structure identification                  
  RFF C 9 Reference                                 
  IMD C 9 Item description                          
  DTM C 99 Date/time/period                          
  group 9 C 99
    QTY M 1 Quantity                                  
    DTM C 99 Date/time/period                          
  endgroup 9
  group 10 C 99
    PRI M 1 Price details                             
    DTM C 99 Date/time/period                          
  endgroup 10
  group 11 C 99
    PCD M 1 Percentage details                        
    DTM C 99 Date/time/period                          
  endgroup 11
  group 12 C 99
    RTE M 1 Rate details                              
    DTM C 99 Date/time/period                          
  endgroup 12
  group 13 C 99
    MOA M 1 Monetary amount                           
    DTM C 99 Date/time/period                          
  endgroup 13
endgroup 8
UNS M 1 Section control                           
group 14 C 99
  RFF M 1 Reference                                 
  group 15 C 999
    SCC M 1 Scheduling conditions                     
    QTY C 99 Quantity                                  
    DTM C 999 Date/time/period                          
  endgroup 15
endgroup 14
group 16 C 9999
  LIN M 1 Line item                                 
  STS C 1 Status                                    
  BII C 9 Structure identification                  
  RFF C 9 Reference                                 
  IMD C 9 Item description                          
  PNA C 9 Party identification                      
  CCI C 9 Characteristic/class id                   
  DTM C 99 Date/time/period                          
  FTX C 99 Free text                                 
  group 17 C 9
    CTA M 1 Contact information                       
    COM C 9 Communication contact                     
  endgroup 17
  group 18 C 99
    QTY M 1 Quantity                                  
    DTM C 99 Date/time/period                          
  endgroup 18
  group 19 C 99
    PCD M 1 Percentage details                        
    DTM C 99 Date/time/period                          
  endgroup 19
  group 20 C 99
    MOA M 1 Monetary amount                           
    DTM C 99 Date/time/period                          
  endgroup 20
  group 21 C 9999
    GIR M 1 Related identification numbers            
    REL C 1 Relationship                              
    RCS C 1 Requirements and conditions               
    BII C 9 Structure identification                  
    RFF C 9 Reference                                 
    IMD C 9 Item description                          
    DTM C 99 Date/time/period                          
    group 22 C 99
      QTY M 1 Quantity                                  
      DTM C 99 Date/time/period                          
    endgroup 22
    group 23 C 99
      PCD M 1 Percentage details                        
      DTM C 99 Date/time/period                          
    endgroup 23
    group 24 C 99
      MOA M 1 Monetary amount                           
      DTM C 99 Date/time/period                          
    endgroup 24
    group 25 C 99
      RTE M 1 Rate details                              
      DTM C 99 Date/time/period                          
    endgroup 25
  endgroup 21
endgroup 16
CNT C 99 Control total                             
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Project tasks planning message is
PROTAP.
Note: Project tasks planning messages conforming to this
document must contain the following data in segment UNH,
composite S009:
Data element  0065 PROTAP
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment to identify a single planning or scheduling reporting
document, its unique identifier, and specific message function.
![GIS]
GIS, General indicator
A segment to identify the report detail type such as network
logic, barchart, milestone, line of balance, or task list type
of information.
![DTM]
DTM, Date/time/period
A segment to identify the message preparation, effective, and
project baseline processing date information.
![g1]
Segment group 1:  RFF-IMD-BII-SG2
A group of segments to provide information about the project
and identify unique reference numbers for the message, to
provide descriptions, to identify structured index numbers, and
to note cross reference links with other projects.
![RFF 1]
RFF, Reference
A segment to identify references related to the plan or
schedule such as a project, program, contract, contract
addendum, Request for Quote, or Request for Proposal number.
![IMD 1]
IMD, Item description
A segment to provide a description such as the name of the
program for any reference number or program.
![BII 1]
BII, Structure identification
A segment to provide a structured index number for any
reference number, project, plan, or schedule.
![g2]
Segment group 2:  GIR-REL
A group of segments to provide related project, plan, or
schedule numbers and relationship type such as a parent,
child, or peer.
![GIR 2]
GIR, Related identification numbers
A segment to identify related or linked project, plan, or
schedule identity numbers.
![REL 2]
REL, Relationship
A segment to identify the type of relationship such as a
parent, child, or peer (internal or external to the
project).
![g3]
Segment group 3:  PNA-ADR-BII-SG4-SG5
A group of segments to identify the parties relevant to the
entire message with related contact and communication
information, provide any applicable structured index number,
and identify the type of data processing environment in use at
the sender or receiver site.
![PNA 3]
PNA, Party identification
A segment to provide message sender and receiver
information.
![ADR 3]
ADR, Address
A segment to provide address information for each named
party.
![BII 3]
BII, Structure identification
A segment to provide a structured index number associated
with each named party.
![g4]
Segment group 4:  CTA-COM
A group of segments to identify contact and communication
numbers for each named party.
![CTA 4]
CTA, Contact information
A segment to provide a contact person's name for each
named party.
![COM 4]
COM, Communication contact
A segment to identify the phone number, fax number, e-
mail, or other communication number for the contact
party.
![g5]
Segment group 5:  CED-DTM
A group of segments to provide information about the data
processing environment used by each named party.
![CED 5]
CED, Computer environment details
A segment to describe the data processing environment
such as the software application name, version and
release.
![DTM 5]
DTM, Date/time/period
A segment to provide the effective, processing, or other
date stamp for the named software application.
![g6]
Segment group 6:  EFI-CED-DTM
A group of segments to provide information about other
documents that relate to the entire message such as cost, other
planning or scheduling, technical, text, or graphical files
that provide additional project reference or management control
information.
![EFI 6]
EFI, External file link identification
A segment to identify a related file and provide details
such as the file name, file format, version, and data
format.
![CED 6]
CED, Computer environment details
A segment to describe the data processing environment used
to create the named file such as the name of the software
application, version and release.
![DTM 6]
DTM, Date/time/period
A segment to provide the created, effective, processing, or
other date stamp for the named file.
![g7]
Segment group 7:  IND-BII-IMD-RCS
A group of segments to identify index structures and contents
of indexing systems used for the plan or schedule.
![IND 7]
IND, Index details
A segment to identify what the index applies to, such as the
entire project or part of a project. Used to indicate
whether IND and related segments describes the index
structure or describes the index contents. When used to
describe a structure, the segment is also used to indicate
the structure hierarchical level.
![BII 7]
BII, Structure identification
A segment to identify a structured index number definition
or contents.
![IMD 7]
IMD, Item description
A segment to provide a description for the structured index
number definition or contents.
![RCS 7]
RCS, Requirements and conditions
A segment to identify an action (add, change, or delete) for
the index data.
![g8]
Segment group 8:  LIN-BII-RFF-IMD-DTM-SG9-SG10-SG11-SG12-SG13
A group of segments to identify company-wide resources that are
available to complete work on any given project. These
resources can be assigned specific working calendars to
indicate the days or times they are available to do work.
![LIN 8]
LIN, Line item
A segment to identify the resource and related action code
(add, change, or delete).
![BII 8]
BII, Structure identification
A segment to identify any structured index numbers for the
resources that are related to the project such as billing or
trade skill categories.
![RFF 8]
RFF, Reference
A segment to provide reference numbers for the resource such
as a working calendar number, charge number or organization
code.
![IMD 8]
IMD, Item description
A segment to provide a description of the resource or type
of resource such as labor or material in clear or coded
form.
![DTM 8]
DTM, Date/time/period
A segment to provide start and end dates related to the
resource.
![g9]
Segment group 9:  QTY-DTM
A group of segments to provide information about the
quantity or number of resources available, a unit of measure
for the resource such as hours, and related dates.
![QTY 9]
QTY, Quantity
A segment to identify the quantity of available resources
and associated unit of measure such as hours or days the
resource is available.
![DTM 9]
DTM, Date/time/period
A segment to identify the effective date, or start and
end dates for the resource quantity.
![g10]
Segment group 10: PRI-DTM
A group of segments to provide a unit price of the resource
and effective dates of the price.
![PRI 10]
PRI, Price details
A segment to identify the resource unit price.
![DTM 10]
DTM, Date/time/period
A segment to identify the effective date, or start and
end dates for the resource unit price.
![g11]
Segment group 11: PCD-DTM
A group of segments to provide availability or efficiency
factors for a given resource with any related dates. This
segment group can also be used to provide an escalation or
inflation percentage that may apply for the resource along
with any associated specific start and end dates.
![PCD 11]
PCD, Percentage details
A segment to identify the percentage of availability or
efficiency factor for the resource; also used to identify
any escalation or inflation percentages.
![DTM 11]
DTM, Date/time/period
A segment to identify the effective date, or start and
end dates for the resource availability, efficiency, or
escalation percentage.
![g12]
Segment group 12: RTE-DTM
A group of segments to provide a rate per unit such as a
rate per hour for the resource and related dates.
![RTE 12]
RTE, Rate details
A segment to provide the rate per unit such as a rate per
hour for the resource.
![DTM 12]
DTM, Date/time/period
A segment to identify the effective date, or start and
end dates for the resource rate per unit.
![g13]
Segment group 13: MOA-DTM
A group of segments to provide any monetary amounts
associated with the resource such as budgeted, planned, or
estimated costs and related dates.
![MOA 13]
MOA, Monetary amount
A segment to identify a monetary amount for the resource.
![DTM 13]
DTM, Date/time/period
A segment to identify the effective date, or start and
end dates for the resource monetary amount.
![UNS]
UNS, Section control
A mandatory service segment placed before the first user
segment in the detail section to avoid segment collision.
![g14]
Segment group 14: RFF-SG15
A group of segments to provide information about the working
calendars used for the tasks and resources assigned to a task.
![RFF 14]
RFF, Reference
A segment to provide the reference used to identify a given
calendar.
![g15]
Segment group 15: SCC-QTY-DTM
A group of segments to describe a given calendar. Provides
information such as specific working patterns, quantity of
working time units per day or working shifts per day, and
specific date details such as non-working or holiday dates.
![SCC 15]
SCC, Scheduling conditions
A segment to provide details about a calendar work period
pattern such as Monday through Friday.
![QTY 15]
QTY, Quantity
A segment to identify the quantity of working time units
per day, quantity of working shifts per day, or quantity
of working time units per shift for a given calendar.
![DTM 15]
DTM, Date/time/period
A segment to provide precise date details about a given
calendar such as specific non-working or holiday dates.
![g16]
Segment group 16: LIN-STS-BII-RFF-IMD-PNA-CCI-DTM-FTX-SG17-
                  SG18-SG19-SG20-SG21
A group of segments to describe the tasks or milestones that
must be done to complete a project. Segments in the group
provide structured index numbers or reference numbers,
descriptions, named responsible person, named work team,
information about the product or service being performed, work
status, dates, durations, other text details, quantity with
optional dates, percents with optional dates, and monetary
amounts with optional dates for the task or milestone.

The subordinate segment group GIR-REL-RCS-BII-RFF-DTM-SG22-
SG23-SG24-SG25 provides related information for milestones
assigned to a main activity or task, interface activities,
constraint activities, or resources assigned to a task.
![LIN 16]
LIN, Line item
A segment to identify the task, indicate if it is an
activity or milestone event, provide an action code (add,
change, delete), and provide a schedule level reference.
![STS 16]
STS, Status
A segment to provide a work status indicator (work has begun
or work has been completed) for the task.
![BII 16]
BII, Structure identification
A segment to identify any structured index numbers such as a
bill of materials or work breakdown structure for the task.
![RFF 16]
RFF, Reference
A segment to provide reference numbers associated with the
line item (task) such as charge numbers, cost accounts,
statement of work numbers, and calendar or work shift
references.
![IMD 16]
IMD, Item description
A segment to provide a description of the task.
![PNA 16]
PNA, Party identification
A segment to provide the party name of a work team for a
given line item (task).
![CCI 16]
CCI, Characteristic/class id
A segment to provide information about a product or assembly
associated with a given line item (task).
![DTM 16]
DTM, Date/time/period
A segment to provide planned start and finish dates, target
dates, actual start and finish dates, estimate start and
finish dates, and other related dates for the line item
(task).
![FTX 16]
FTX, Free text
A segment to provide additional text for the line item
(task). Can be used to provide additional details about the
task, events surrounding the task such as an explanation for
a behind schedule over cost situation, or further
explanation about data associated with the task.
![g17]
Segment group 17: CTA-COM
A group of segments to provide a responsible person's name
and communication numbers for the line item (task).
![CTA 17]
CTA, Contact information
A segment to provide a responsible person's name for the
line item (task).
![COM 17]
COM, Communication contact
A segment to identify the phone number, fax number, e-
mail, or other communication number of the responsible
person for the line item (task).
![g18]
Segment group 18: QTY-DTM
A group of segments to provide quantities and related unit
of measure associated with a line item with related dates.
May be used to convey planned, current, or progress
durations when expressed as a quantity of units of time or a
total quantity such as planned or budgeted hours for the
task. Progress can also be conveyed as a percentage (segment
group PCD-DTM) or as dates (independent DTM segment). All
three methods can be used independently to exchange progress
data; typically one of these three methods is preferred and
agreed upon between trading parties.
![QTY 18]
QTY, Quantity
A segment to provide the unit of measure (such as hours)
and quantities (such as budget, actual, or earned)
associated with a line item (task). Also used to provide
the duration of the task when expressed as a quantity of
time units such as 10 working days; lead time duration
such as a lead of 5 working days is required before the
next task can start; and progress information such as a
remaining duration of 5 working days.
![DTM 18]
DTM, Date/time/period
A segment to provide a single pair of start and end dates
or a series of start and end dates (for example, to
describe monthly allocations) associated with hour
quantities (budget, actual, earned value) for the task.
![g19]
Segment group 19: PCD-DTM
A group of segments to provide progress details about a line
item and any related dates when progress is expressed as a
percentage. Progress can also be conveyed as a duration
quantity (segment group QTY-DTM) or as dates (independent
DTM segment). All three methods can be used independently to
exchange progress data; typically one of these three methods
is preferred and agreed upon between trading parties.
![PCD 19]
PCD, Percentage details
A segment to provide the percent complete for the line
item (task).
![DTM 19]
DTM, Date/time/period
A segment to provide effective, or start and end dates
for the percent complete for the task.
![g20]
Segment group 20: MOA-DTM
A group of segments to provide monetary amounts associated
with a line item and any related dates.
![MOA 20]
MOA, Monetary amount
A segment to provide monetary amounts (such as budget,
actual, or earned value costs) for the line item (task).
![DTM 20]
DTM, Date/time/period
A segment to provide a single pair of start and end dates
or a series of start and end dates (for example, to
describe costs broken down by month) monetary amounts for
the task.
![g21]
Segment group 21: GIR-REL-RCS-BII-RFF-IMD-DTM-SG22-SG23-
                  SG24-SG25
A group of segments to identify a related interface
activity, constraint activity, assigned resources, or
milestones events (when the line item is an activity)
assigned to the line item (task) along with related
structured index or reference numbers, descriptions, work
status, dates, and any associated quantity, percent,
monetary amounts, and rates.
![GIR 21]
GIR, Related identification numbers
A segment to identify a related constraint activity,
interface activity, assigned resource, or milestone event
(when the line item is an activity). For constraint
activities, also used to indicate if the cited constraint
activity is a predecessor or successor. For milestone
events associated with a task, also used to provide a
work status indicator (work has begun, work has been
completed, or the milestone event has been revised).
![REL 21]
REL, Relationship
A segment to identify the type of relationship. Used for
constraint type of activities to describe finish to
start, start to start, start to finish, and finish to
finish types of relationships.
![RCS 21]
RCS, Requirements and conditions
A segment to identify an action (add, change, or delete)
for the interface activity, constraint activity, assigned
resource, or milestone event.
![BII 21]
BII, Structure identification
A segment to identify any structured index numbers such
an organization breakdown structure, or trade skill for
an assigned resource.
![RFF 21]
RFF, Reference
A segment to provide reference numbers associated with
the interface activity, constraint activity, assigned
resource, or milestone event such as a calendar or shift,
department, work package, charge number, or work order
number.
![IMD 21]
IMD, Item description
A segment to provide a description of the interface
activity, constraint activity, assigned resource, or
milestone event. When an assigned resource, can also use
this segment to indicate the type of resource in coded
form (such as a consumable, recurring, or non-recurring
resource).
![DTM 21]
DTM, Date/time/period
A segment to provide start and finish dates, actual start
and finish dates, scheduled start and finish dates,
planned or revised dates for assigned resources or
milestones.
![g22]
Segment group 22: QTY-DTM
A group of segments to provide quantity information for
assigned resources or milestones events and related
dates. Also used to provide the duration of lead or lag
time for an interface activity or constraint activity
when the duration is expressed as a quantity of time
units such as a lead of 5 working days or a lag or 7
working days.
![QTY 22]
QTY, Quantity
A segment to provide quantity information about the
interface activity, constraint activity, assigned
resource, or milestone event. For activities, used to
provide the duration of lead or lag time when
expressed as quantities of time units. For assigned
resources, can be used to indicate the number of
resources available for the task (level per calendar
unit or total for the duration of the task). For
assigned resources and milestone events, can be used
to provide associated budget, actual, or earned value
hour quantities.
![DTM 22]
DTM, Date/time/period
A segment to provide an effective date, a pair of
start and end dates, or a series of start and end
dates associated with an interface activity,
constraint activity, assigned resource, or milestone
quantity.
![g23]
Segment group 23: PCD-DTM
A group of segments to provide an earned value percentage
for milestone events or to provide availability or
efficiency factors for assigned resources and any related
dates.
![PCD 23]
PCD, Percentage details
A segment to provide availability or efficiency
factors for an assigned resource or to provide an
earned value percentage for a given milestone (used to
calculate the amount of value earned when a milestone
is complete).
![DTM 23]
DTM, Date/time/period
A segment to provide an effective date, or start and
end dates associated with the availability,
efficiency, or earned value percentage.
![g24]
Segment group 24: MOA-DTM
A group of segments to provide monetary amounts for
assigned resources and milestone events and any related
dates.
![MOA 24]
MOA, Monetary amount
A segment to provide monetary amounts (such as budget,
actual, or earned value) for the assigned resource or
milestone event.
![DTM 24]
DTM, Date/time/period
A segment to provide an effective date, a pair of
start and finish dates, or a series of start and
finish dates associated with the budget, actual, or
earned value monetary amount for the assigned resource
or milestone event.
![g25]
Segment group 25: RTE-DTM
A group of segments to provide rate information for an
assigned resource and any related dates.
![RTE 25]
RTE, Rate details
A segment to identify a rate for the assigned
resource.
![DTM 25]
DTM, Date/time/period
A segment to provide the effective date, or start and
end dates for the assigned resource rate.
![CNT]
CNT, Control total
A segment to provide control totals for the message such as the
total number of activities, milestones, resources, or
constraints.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
