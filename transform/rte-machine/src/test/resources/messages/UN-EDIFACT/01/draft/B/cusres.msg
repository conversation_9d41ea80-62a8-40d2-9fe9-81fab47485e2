#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                       Customs response message
#
#
#
#
#====================================================================
MESSAGE=CUSRES
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM C 9 Date/time/period                          
FTX C 99 Free text                                 
TDT C 9 Details of transport                      
LOC C 99 Place/location identification             
GIS C 10 General indicator                         
EQD C 999 Equipment details                         
group 1 C 9
  NAD M 1 Name and address                          
  group 2 C 9
    CTA M 1 Contact information                       
    COM C 9 Communication contact                     
  endgroup 2
endgroup 1
group 3 C 999
  RFF M 1 Reference                                 
  DTM C 9 Date/time/period                          
  LOC C 9 Place/location identification             
endgroup 3
group 4 C 99
  ERP M 1 Error point details                       
  RFF C 9 Reference                                 
  ERC C 99 Application error information             
  FTX C 99 Free text                                 
endgroup 4
group 5 C 99
  TAX M 1 Duty/tax/fee details                      
  MOA C 99 Monetary amount                           
  GIS C 99 General indicator                         
endgroup 5
group 6 C 9999
  DOC M 1 Document/message details                  
  PAC C 9 Package                                   
  RFF C 9 Reference                                 
  PCI C 9 Package identification                    
  FTX C 9 Free text                                 
  TDT C 9 Details of transport                      
  LOC C 9 Place/location identification             
  DTM C 9 Date/time/period                          
  GIS C 9 General indicator                         
  MEA C 99 Measurements                              
  EQD C 999 Equipment details                         
  group 7 C 9
    NAD M 1 Name and address                          
    group 8 C 9
      CTA M 1 Contact information                       
      COM C 9 Communication contact                     
    endgroup 8
  endgroup 7
  group 9 C 99
    MOA M 1 Monetary amount                           
    CUX C 9 Currencies                                
  endgroup 9
  group 10 C 99
    TAX M 1 Duty/tax/fee details                      
    MOA C 99 Monetary amount                           
    GIS C 99 General indicator                         
  endgroup 10
  group 11 C 9999
    CST M 1 Customs status of goods                   
    FTX C 9 Free text                                 
    group 12 C 999999
      TAX M 1 Duty/tax/fee details                      
      MOA C 99 Monetary amount                           
      GIS C 99 General indicator                         
      MEA C 9 Measurements                              
      RFF C 9 Reference                                 
    endgroup 12
  endgroup 11
  group 13 C 9999
    ERP M 1 Error point details                       
    RFF C 9 Reference                                 
    ERC C 9999 Application error information             
    FTX C 9 Free text                                 
  endgroup 13
endgroup 6
CNT C 9 Control total                             
group 14 C 9
  AUT M 1 Authentication result                     
  DTM C 9 Date/time/period                          
endgroup 14
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Customs response message is
CUSRES.
Note: Customs response messages conforming to this document
must contain the following data in segment UNH, composite S009:
Data element  0065 CUSRES
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment identifying the type and the reference number of the
message to which the CUSRES is a response.
![DTM]
DTM, Date/time/period
A segment identifying the relevant dates and times in the
message. For example, payment date.
![FTX]
FTX, Free text
A segment stating information in unsolicited clear text.
![TDT]
TDT, Details of transport
A segment to specify any means of transport that is related to
all information at the lower level of the message.
![LOC]
LOC, Place/location identification
A segment to identify a country and/or place and/or location
information related to the whole message.
![GIS]
GIS, General indicator
A segment identifying the various customs processing
indicators. For example, cargo released, cargo held,
examination required, earlier message accepted or rejected,
etc.
![EQD]
EQD, Equipment details
A segment identifying equipment initials and numbers required
to identify a shipment, covering the whole message, e.g. to
indicate the container number that Customs wants to examine.
![g1]
Segment group 1:  NAD-SG2
A group of segments identifying party details, including
contact and communication contact information.
![NAD 1]
NAD, Name and address
A segment to identify the name and/or address of the party
related to all information at the lower level of the
message. For example, the importer.
![g2]
Segment group 2:  CTA-COM
A group of segments identifying contact and communication
contact information.
![CTA 2]
CTA, Contact information
A segment to identify a person or a department to whom
communication should be directed.
![COM 2]
COM, Communication contact
A segment to identify a communication number of a
department or a person to whom communication should be
directed.
![g3]
Segment group 3:  RFF-DTM-LOC
A group of segments identifying references, dates and locations
related information.
![RFF 3]
RFF, Reference
A segment identifying references (e.g. manifest number).
![DTM 3]
DTM, Date/time/period
A segment identifying a date related to the preceding RFF.
![LOC 3]
LOC, Place/location identification
A segment identifying a location related to the preceding
RFF.
![g4]
Segment group 4:  ERP-RFF-ERC-FTX
A group of segments identifying an application error condition
within a message to which the CUSRES is a response.
![ERP 4]
ERP, Error point details
A segment identifying the location of an application error
within the referenced message.
![RFF 4]
RFF, Reference
A segment to provide the references related to the
application error.
![ERC 4]
ERC, Application error information
A segment identifying the type of application errors within
a message.
![FTX 4]
FTX, Free text
A segment to provide explanation and/or supplementary
information related to the specified application error.
![g5]
Segment group 5:  TAX-MOA-GIS
A group of segments identifying the customs amounts for duties,
taxes and fees, following computation by customs, for all
customs declarations.
![TAX 5]
TAX, Duty/tax/fee details
A segment identifying the tax and/or duty and/or fee type,
rate and base totals for all customs declarations.
![MOA 5]
MOA, Monetary amount
A segment to identify a tax and/or duty and/or fee amount
related to TAX.
![GIS 5]
GIS, General indicator
A segment identifying method of payment.
![g6]
Segment group 6:  DOC-PAC-RFF-PCI-FTX-TDT-LOC-DTM-GIS-MEA-EQD-
                  SG7-SG9-SG10-SG11-SG13
A group of segments specifying goods item details including
status of goods, related references, package identification,
details of transport, relevant parties, contacts, dates and/or
times and location, general indicators, measurements,
documentary requirements, monetary amounts, currencies and item
taxes, fees and duties.
![DOC 6]
DOC, Document/message details
A segment to identify the master bill number reported in a
multi-consignment message. This segment can also be used to
identify the Customs declaration where the CUSRES is
responding to multiple declarations.
![PAC 6]
PAC, Package
A segment identifying number and type of packages.
![RFF 6]
RFF, Reference
A segment identifying a particular reference, e.g. house of
bill number, master bill number.
![PCI 6]
PCI, Package identification
A segment identifying marking and labels on individual
shipping or packing units.
![FTX 6]
FTX, Free text
A segment to provide the goods description.
![TDT 6]
TDT, Details of transport
A segment to identify any means of transport that is related
to a part of the information in the message. For example,
when one CUSRES message contains more than one release note.
![LOC 6]
LOC, Place/location identification
A segment identifying the places relevant to the item, which
is different from the places identified at header level. For
example, customs house of entry, warehouse, etc.
![DTM 6]
DTM, Date/time/period
A segment identifying necessary dates.
![GIS 6]
GIS, General indicator
A segment identifying customs processing indicator at item
level.
![MEA 6]
MEA, Measurements
Segment identifying various measurement values.
![EQD 6]
EQD, Equipment details
A segment identifying equipment initials and numbers.
![g7]
Segment group 7:  NAD-SG8
A group of segments identifying information on the party and
other contact and communication contact information related
only to part of the message.
![NAD 7]
NAD, Name and address
A segment identifying information about a party related
only to a part of the message. For example, in a message
with several release notes or in a message related to
several declarations, a customs agent and the person
responsible for the information.
![g8]
Segment group 8:  CTA-COM
A group of segments identifying contact and communication
contact information.
![CTA 8]
CTA, Contact information
A segment to identify a person or a department to whom
communication should be directed.
![COM 8]
COM, Communication contact
A segment to identify a communication number of a
department or a person to whom communication should be
directed.
![g9]
Segment group 9:  MOA-CUX
A group of segments identifying the monetary amount,
currencies and the rate of exchange, against which duties,
taxes or fees are being computed, per item.
![MOA 9]
MOA, Monetary amount
A segment identifying the monetary amount.
![CUX 9]
CUX, Currencies
A segment identifying currencies and the rate of exchange
for a monetary amount identified within this group.
![g10]
Segment group 10: TAX-MOA-GIS
A group of segments identifying customs amounts for taxes,
duties and fees related to a single customs declaration
(e.g. a simplified customs declaration).
![TAX 10]
TAX, Duty/tax/fee details
A segment identifying tax/duty/fee related to a single
customs declaration.
![MOA 10]
MOA, Monetary amount
A segment identifying tax/duty/fee amounts related to
TAX.
![GIS 10]
GIS, General indicator
A segment identifying the methods of payment related to
TAX.
![g11]
Segment group 11: CST-FTX-SG12
A group of segments identifying goods in terms of customs
identities and customs amounts for duties, taxes and fees,
following computation by customs.
![CST 11]
CST, Customs status of goods
A segment identifying goods in terms of customs
identities, status and intended use.
![FTX 11]
FTX, Free text
A segment to specify the goods description.
![g12]
Segment group 12: TAX-MOA-GIS-MEA-RFF
A group of segments identifying the customs amounts for-
duties, taxes and fees related to the customs items
identified in CST.
![TAX 12]
TAX, Duty/tax/fee details
A segment identifying the tax and/or duty and/or fee
type, rate and base for customs item amounts.
![MOA 12]
MOA, Monetary amount
A segment identifying a tax and/or duty and/or fee
amount related to TAX.
![GIS 12]
GIS, General indicator
A segment identifying method of payment.
![MEA 12]
MEA, Measurements
A segment to identify measurements related to the
calculation of the customs duties.
![RFF 12]
RFF, Reference
A segment to identify reference numbers related to the
tax calculations (e.g. reference number for VAT free
allowance).
![g13]
Segment group 13: ERP-RFF-ERC-FTX
A group of segments identifying an application error
condition related only to a part of the message.
![ERP 13]
ERP, Error point details
A segment identifying the location of an application
error.
![RFF 13]
RFF, Reference
A segment to provide the references related to the
application error.
![ERC 13]
ERC, Application error information
A segment identifying the type of application errors.
![FTX 13]
FTX, Free text
A segment to provide explanation and/or supplementary
information related to the specified application error.
![CNT]
CNT, Control total
A segment to identify control totals.
![g14]
Segment group 14: AUT-DTM
A group of segments permitting the verification of the
authenticity of the sender and the integrity of the data.
![AUT 14]
AUT, Authentication result
A segment containing the result of the algorithm permitting
the verification of the authenticity of the sender and the
integrity of the data.
![DTM 14]
DTM, Date/time/period
A segment identifying a date associated with the preceding
AUT.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
