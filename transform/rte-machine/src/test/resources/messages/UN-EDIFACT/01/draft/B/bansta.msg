#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                        Banking status message
#
#
#
#
#====================================================================
MESSAGE=BANSTA
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM M 1 Date/time/period                          
BUS C 1 Business function                         
group 1 C 2
  RFF M 1 Reference                                 
  DTM C 1 Date/time/period                          
endgroup 1
group 2 C 5
  FII M 1 Financial institution information         
  CTA C 1 Contact information                       
  COM C 5 Communication contact                     
endgroup 2
group 3 C 3
  NAD M 1 Name and address                          
  CTA C 1 Contact information                       
  COM C 5 Communication contact                     
endgroup 3
group 4 M 999
  LIN M 1 Line item                                 
  group 5 C 5
    RFF M 1 Reference                                 
    DTM C 1 Date/time/period                          
  endgroup 5
  group 6 C 99
    SEQ M 1 Sequence details                          
    GIS M 1 General indicator                         
    DTM C 2 Date/time/period                          
    MOA C 1 Monetary amount                           
    CUX C 1 Currencies                                
    PCD C 1 Percentage details                        
    FTX C 1 Free text                                 
    DOC C 5 Document/message details                  
    group 7 C 1
      FII M 1 Financial institution information         
      CTA C 1 Contact information                       
      COM C 5 Communication contact                     
    endgroup 7
    group 8 C 1
      NAD M 1 Name and address                          
      CTA C 1 Contact information                       
      COM C 5 Communication contact                     
    endgroup 8
  endgroup 6
endgroup 4
CNT C 5 Control total                             
group 9 C 5
  AUT M 1 Authentication result                     
  DTM C 1 Date/time/period                          
endgroup 9
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Banking status message is BANSTA.
Note: Banking status messages conforming to this document must
contain the following data in segment UNH, composite S009:
Data element  0065 BANSTA
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment by means of which the sender must uniquely identify
the BANSTA message using its type and number and its function
(e.g. response, enquiry, status etc.).
![DTM]
DTM, Date/time/period
A segment specifying the date and, if required, the time the
message is created.
![BUS]
BUS, Business function
A segment identifying certain characteristics of the Banking
Status message, such as its business function.
![g1]
Segment group 1:  RFF-DTM
A group of segments identifying a previously-sent message,
and/or customer to customer reference and related dates.
![RFF 1]
RFF, Reference
A segment specifying the reference of the previously-sent
message.
![DTM 1]
DTM, Date/time/period
A segment identifying the creation date of the referenced
message.
![g2]
Segment group 2:  FII-CTA-COM
A group of segments identifying the financial institutions
involved in the Banking Status message, routing functions and
contacts.
![FII 2]
FII, Financial institution information
A segment identifying the financial institution(s)
associated with the transaction, in coded or uncoded form
and their function.
![CTA 2]
CTA, Contact information
A segment identifying a person or a department for the party
specified in the leading FII segment to whom communication
should be directed.
![COM 2]
COM, Communication contact
A segment identifying communication type(s) and number(s) of
person(s) or department(s) specified in the associated CTA
segment.
![g3]
Segment group 3:  NAD-CTA-COM
A group of segments identifying the name(s) and address(es) of
non-financial parties involved in the transaction, their
function and their contacts.
![NAD 3]
NAD, Name and address
A segment identifying the names and addresses of the non-
financial parties associated with the Banking Status
message, in coded or uncoded form, and their function.
![CTA 3]
CTA, Contact information
A segment identifying a person or department for the party
specified in the NAD segment and to whom communication
should be directed.
![COM 3]
COM, Communication contact
A segment identifying communication type(s) and number(s) of
person(s) or department(s) specified in the associated CTA
segment.
![g4]
Segment group 4:  LIN-SG5-SG6
A group of segments identifying a message or transaction and
the status of the referred message/transaction, as well as any
reasons clarifying the status.
![LIN 4]
LIN, Line item
A segment identifying the beginning of the details related
to the previously-sent message by a sequential line number.
![g5]
Segment group 5:  RFF-DTM
A group of segments specifying reference number(s), date/or
time needed in order to identify a referenced message or
transaction.
![RFF 5]
RFF, Reference
A segment providing references of the message/transaction
to be referred to.
![DTM 5]
DTM, Date/time/period
A segment identifying the date/time of the referred
message or transaction.
![g6]
Segment group 6:  SEQ-GIS-DTM-MOA-CUX-PCD-FTX-DOC-SG7-SG8
A group of segments identifying the status, and any reasons
clarifying this status, of the referred message/transaction.
![SEQ 6]
SEQ, Sequence details
A segment identifying the beginning of the specification
of the status and related details about the
message/transaction by a sequential number.
![GIS 6]
GIS, General indicator
A segment specifying the processing status of a
referenced message/transaction in a coded form. It is
used in conjunction with one or more of the following
segments or groups clarifying the detailed reply or
status/information.
![DTM 6]
DTM, Date/time/period
A segment identifying dates and/or times related to
information in the GIS-segment.
![MOA 6]
MOA, Monetary amount
A segment identifying the amount(s) associated with the
related information in the GIS-segment.
![CUX 6]
CUX, Currencies
A segment identifying the currency associated with the
related information in the GIS segment.
![PCD 6]
PCD, Percentage details
A segment identifying the percentage associated with the
related information in the GIS segment.
![FTX 6]
FTX, Free text
A segment specifying free form data associated with the
related information in the GIS segment.
![DOC 6]
DOC, Document/message details
A segment identifying the documents associated with the
related information in the GIS segment.
![g7]
Segment group 7:  FII-CTA-COM
A group of segments identifying the financial
institution(s) associated with the related information in
the GIS segment.
![FII 7]
FII, Financial institution information
A segment identifying the financial institution(s)
associated with the related information in the GIS
segment.
![CTA 7]
CTA, Contact information
A segment identifying a person or a department for the
party specified in the leading FII segment to whom
communication should be directed.
![COM 7]
COM, Communication contact
A segment identifying communication type(s) and
number(s) of person(s) or departments(s) specified in
the associated CTA segment.
![g8]
Segment group 8:  NAD-CTA-COM
A group of segments identifying the name and address of
non-financial parties associated with the related
information in the GIS segment.
![NAD 8]
NAD, Name and address
A segment identifying names and address of non-
financial parties associated with the related
information in the GIS segment.
![CTA 8]
CTA, Contact information
A segment identifying a person or a department for the
party specified in the leading NAD segment to whom
communication should be directed.
![COM 8]
COM, Communication contact
A segment identifying communication type(s) and
number(s) of person(s) or departments(s) specified in
the associated CTA segment.
![CNT]
CNT, Control total
A segment identifying the kind of control-checks and the total
according to the coded form.
![g9]
Segment group 9:  AUT-DTM
A group of segments specifying details of any authentication
(validation) procedures applied to the BANSTA message.
![AUT 9]
AUT, Authentication result
A segment specifying the details of any authentication
(validation) procedures applied to the BANSTA message.
![DTM 9]
DTM, Date/time/period
A segment identifying the validation date/time.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
