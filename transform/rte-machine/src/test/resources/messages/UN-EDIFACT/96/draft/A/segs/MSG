MESSAGE TYPE IDENTIFICATION
#Release:
To identify a message type and to give its class and maintenance operation.
#--------------------------------------------------------
ELEMENTS
#--------------------------------------------------------

C709 M  MESSAGE IDENTIFIER
  1475 M an..6 Message type identifier
  1056 M an..9 Version
  1058 M an..9 Release
  1476 M an..2 Control agency
  1523 C an..6 Association assigned identification
  1060 C an..6 Revision number
1507 C an..3 CLASS DESIGNATOR, CODED
4513 C an..3 MAINTENANCE OPERATION, CODED