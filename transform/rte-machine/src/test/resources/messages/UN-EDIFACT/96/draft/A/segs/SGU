SEGMENT USAGE DETAILS
#Release:
To specify the details of the usage of a segment within a message type structure.
#--------------------------------------------------------
ELEMENTS
#--------------------------------------------------------

9166 <USER> <GROUP>..3 SEGMENT TAG
7299 C an..3 REQUIREMENT DESIGNATOR, CODED
6176 C n..7 MAXIMUM NUMBER OF OCCURRENCES
7168 C n1 LEVEL NUMBER
1050 C an..6 SEQUENCE NUMBER
1049 C an..3 MESSAGE SECTION, CODED
4513 C an..3 MAINTENANCE OPERATION, CODED